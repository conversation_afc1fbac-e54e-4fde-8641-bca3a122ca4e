<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>韩国旅游 · Sky Mirror World - 马来西亚出发韩国包团旅游</title>
  <link rel="canonical" href="" />
  <link rel="stylesheet" href="/css/base.css" />
  <link rel="stylesheet" href="/css/unified-pages.css" />
  <link rel="stylesheet" href="/css/enhanced-unified.css" />
  <link rel="stylesheet" href="/css/enhanced-effects.css" />
  <meta name="description" content="韩国旅游：首尔釜山、济州岛包团旅游。K-POP文化、美食购物、专业中文导游，马来西亚出发。" />
  <meta property="og:title" content="韩国旅游包团 - Sky Mirror World" />
  <meta property="og:description" content="马来西亚出发韩国深度游：首尔、釜山、济州岛等热门目的地" />
  <meta property="og:type" content="website" />
  <meta property="og:image" content="https://images.unsplash.com/photo-1540961363010-18275e8e7dfc?w=1200&h=630&fit=crop" />
  
  <!-- Google Fonts & Font Awesome -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
</head>
<body class="gradient-bg">
  <header class="site-header">
    <div class="container">
      <a class="logo" href="/">
        <i class="fas fa-mirror"></i>
        <span>Sky Mirror World</span>
      </a>
      <nav class="top-nav">
        <a href="/outbound/" class="active">境外旅游</a>
        <a href="/transport/">马新用车服务</a>
        <a href="/mice/">MICE</a>
        <a href="/islands/">海岛游</a>
        <a href="/mm2h/">MM2H</a>
      </nav>

      <button class="mobile-menu-toggle" aria-label="打开菜单">
        <i class="fas fa-bars"></i>
      </button>
    </div>
  </header>

  <main>
    <!-- Hero Section -->
    <section class="hero-banner korea-hero">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="glow-text">🇰🇷 韩国深度游</h1>
          <p class="typewriter">感受韩流文化 · 马来西亚出发包团旅游</p>
          <div class="hero-stats">
            <div class="stat-item fade-in">
              <span class="counter" data-target="600">0</span>+
              <small>成功团队</small>
            </div>
            <div class="stat-item fade-in">
              <span class="counter" data-target="97">0</span>%
              <small>满意度</small>
            </div>
            <div class="stat-item fade-in">
              <span class="counter" data-target="80">0</span>+
              <small>热门景点</small>
            </div>
          </div>
          <div class="cta-buttons">
            <a href="/quote/" class="pulse-btn">立即报价</a>
            <a href="#products" class="magnetic-btn">查看行程</a>
          </div>
        </div>
        <div class="hero-visual">
          <div class="floating-card flip-card">
            <div class="flip-card-inner">
              <div class="flip-card-front">
                <img src="https://images.unsplash.com/photo-1540961363010-18275e8e7dfc?w=400&h=300&fit=crop" alt="韩国风景" />
                <h3>韩流文化</h3>
              </div>
              <div class="flip-card-back">
                <h3>🎵 K-Culture</h3>
                <p>探索韩流文化与现代时尚的完美融合</p>
                <ul>
                  <li>K-POP体验</li>
                  <li>韩式美食</li>
                  <li>美妆购物</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="wave-divider"></div>
    </section>

    <!-- Navigation Breadcrumb -->
    <nav class="breadcrumb" aria-label="面包屑导航">
      <div class="container">
        <a href="/">首页</a>
        <i class="fas fa-chevron-right"></i>
        <a href="/outbound/">境外旅游</a>
        <i class="fas fa-chevron-right"></i>
        <span>韩国</span>
      </div>
    </nav>

    <!-- Featured Destinations -->
    <section class="destinations-grid" id="products">
      <div class="container">
        <div class="section-header">
          <h2 class="rainbow-text">热门韩国目的地</h2>
          <p>精选韩国最受欢迎的旅游城市和文化胜地</p>
        </div>

        <div class="card-grid card-grid-3">
          <!-- 首尔经典 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1540961363010-18275e8e7dfc?w=400&h=250&fit=crop" alt="首尔" loading="lazy" />
              <div class="card-overlay">
                <div class="destination-info">
                  <h3 class="card-title">🏙️ 首尔经典游</h3>
                  <p class="card-description">韩流发源地，体验最纯正的K-Culture和现代都市风情</p>
                  <div class="price-badge">从 RM 2,199 起</div>
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-tags">
                <span class="card-tag hot">热门</span>
                <span class="card-tag">文化</span>
                <span class="card-tag">5天4晚</span>
              </div>
              <div class="highlights">
                <span><i class="fas fa-building"></i> 明洞购物</span>
                <span><i class="fas fa-crown"></i> 景福宫</span>
                <span><i class="fas fa-music"></i> 弘大夜生活</span>
              </div>
              <div class="card-actions">
                <a href="seoul-classic/" class="btn btn-primary">
                  <i class="fas fa-eye"></i> 查看详情
                </a>
                <button class="btn btn-secondary" onclick="addToCompare('seoul-classic')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- 首尔釜山 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=250&fit=crop" alt="首尔釜山" loading="lazy" />
              <div class="card-overlay">
                <div class="destination-info">
                  <h3 class="card-title">🌊 首尔 + 釜山</h3>
                  <p class="card-description">都市与海岸的完美结合，感受不同的韩国风情</p>
                  <div class="price-badge">从 RM 2,599 起</div>
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-tags">
                <span class="card-tag">双城</span>
                <span class="card-tag">海岸</span>
                <span class="card-tag">6天5晚</span>
              </div>
              <div class="highlights">
                <span><i class="fas fa-water"></i> 海云台海滩</span>
                <span><i class="fas fa-mountain"></i> 甘川文化村</span>
                <span><i class="fas fa-fish"></i> 釜山海鲜</span>
              </div>
              <div class="card-actions">
                <a href="seoul-busan/" class="btn btn-primary">
                  <i class="fas fa-eye"></i> 查看详情
                </a>
                <button class="btn btn-secondary" onclick="addToCompare('seoul-busan')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- 济州岛 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=250&fit=crop" alt="济州岛" loading="lazy" />
              <div class="card-overlay">
                <div class="destination-info">
                  <h3 class="card-title">🏝️ 济州岛度假</h3>
                  <p class="card-description">韩国的夏威夷，火山岛美景与韩剧取景地</p>
                  <div class="price-badge">从 RM 1,899 起</div>
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-tags">
                <span class="card-tag">海岛</span>
                <span class="card-tag">自然</span>
                <span class="card-tag">4天3晚</span>
              </div>
              <div class="highlights">
                <span><i class="fas fa-mountain"></i> 汉拿山</span>
                <span><i class="fas fa-water"></i> 海女表演</span>
                <span><i class="fas fa-heart"></i> 韩剧景点</span>
              </div>
              <div class="card-actions">
                <a href="jeju/" class="btn btn-primary">
                  <i class="fas fa-eye"></i> 查看详情
                </a>
                <button class="btn btn-secondary" onclick="addToCompare('jeju')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- K-POP文化游 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=250&fit=crop" alt="K-POP" loading="lazy" />
              <div class="card-overlay">
                <span class="price-tag">从 RM 2,399 起</span>
                <div class="card-badges">
                  <span class="badge kpop">K-POP</span>
                  <span class="badge special">主题</span>
                </div>
              </div>
            </div>
            <div class="card-content">
              <h3>K-POP文化深度游</h3>
              <p class="duration"><i class="fas fa-clock"></i> 5天4晚</p>
              <p class="description">追星必选，深度体验韩流文化和K-POP产业</p>
              <div class="highlights">
                <span><i class="fas fa-music"></i> SM Town</span>
                <span><i class="fas fa-tv"></i> 电视台参观</span>
                <span><i class="fas fa-microphone"></i> 练习生体验</span>
              </div>
              <div class="card-actions">
                <a href="kpop-tour/" class="btn btn-primary">查看详情</a>
                <button class="btn btn-secondary" onclick="addToCompare('kpop-tour')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- 美妆购物游 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1556905055-8f358a7a47b2?w=400&h=250&fit=crop" alt="美妆购物" loading="lazy" />
              <div class="card-overlay">
                <span class="price-tag">从 RM 2,099 起</span>
                <div class="card-badges">
                  <span class="badge beauty">美妆</span>
                  <span class="badge shopping">购物</span>
                </div>
              </div>
            </div>
            <div class="card-content">
              <h3>美妆购物专线</h3>
              <p class="duration"><i class="fas fa-clock"></i> 4天3晚</p>
              <p class="description">女生最爱，韩式美妆和时尚购物的完美体验</p>
              <div class="highlights">
                <span><i class="fas fa-shopping-bag"></i> 明洞购物</span>
                <span><i class="fas fa-spa"></i> 美容体验</span>
                <span><i class="fas fa-palette"></i> 化妆课程</span>
              </div>
              <div class="card-actions">
                <a href="beauty-shopping/" class="btn btn-primary">查看详情</a>
                <button class="btn btn-secondary" onclick="addToCompare('beauty-shopping')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- 韩食文化游 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1589302168068-964664d93dc0?w=400&h=250&fit=crop" alt="韩食" loading="lazy" />
              <div class="card-overlay">
                <span class="price-tag">从 RM 2,299 起</span>
                <div class="card-badges">
                  <span class="badge food">美食</span>
                  <span class="badge culture">文化</span>
                </div>
              </div>
            </div>
            <div class="card-content">
              <h3>韩食文化深度游</h3>
              <p class="duration"><i class="fas fa-clock"></i> 5天4晚</p>
              <p class="description">吃货必选，品尝正宗韩式料理和街头小食</p>
              <div class="highlights">
                <span><i class="fas fa-utensils"></i> 烤肉体验</span>
                <span><i class="fas fa-pepper-hot"></i> 泡菜制作</span>
                <span><i class="fas fa-fire"></i> 街头小食</span>
              </div>
              <div class="card-actions">
                <a href="food-culture/" class="btn btn-primary">查看详情</a>
                <button class="btn btn-secondary" onclick="addToCompare('food-culture')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- K-Culture Experience Section -->
    <section class="k-culture-experience">
      <div class="container">
        <div class="section-header">
          <h2>🎵 韩流文化体验</h2>
          <p>深度体验最纯正的韩国文化</p>
        </div>
        
        <div class="experience-grid">
          <div class="experience-card floating-card">
            <div class="experience-icon">
              <i class="fas fa-music"></i>
            </div>
            <h3>K-POP体验</h3>
            <p>参观娱乐公司，观看现场录制，甚至有机会见到偶像。体验练习生生活，学习韩式舞蹈。</p>
            <div class="experience-highlight">包含：SM Town、JYP娱乐参观</div>
          </div>
          
          <div class="experience-card floating-card">
            <div class="experience-icon">
              <i class="fas fa-tv"></i>
            </div>
            <h3>韩剧取景地</h3>
            <p>走访热门韩剧拍摄地，如《爱的迫降》、《鬼怪》等经典场景，重现剧中浪漫时刻。</p>
            <div class="experience-highlight">包含：景福宫、南山塔、梨花大学</div>
          </div>
          
          <div class="experience-card floating-card">
            <div class="experience-icon">
              <i class="fas fa-palette"></i>
            </div>
            <h3>韩式美妆</h3>
            <p>体验韩式化妆技巧，参观化妆品工厂，学习韩国护肤秘诀，购买最新美妆产品。</p>
            <div class="experience-highlight">包含：化妆课程、美容院体验</div>
          </div>
          
          <div class="experience-card floating-card">
            <div class="experience-icon">
              <i class="fas fa-utensils"></i>
            </div>
            <h3>韩食文化</h3>
            <p>学习制作泡菜、韩式烤肉，品尝正宗韩国料理，探访传统市场和现代美食街。</p>
            <div class="experience-highlight">包含：料理课程、市场导览</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Shopping Guide Section -->
    <section class="shopping-guide">
      <div class="container">
        <div class="section-header">
          <h2>🛍️ 韩国购物指南</h2>
          <p>必买商品和购物热点推荐</p>
        </div>
        
        <div class="shopping-categories">
          <div class="category-card floating-card">
            <div class="category-icon">
              <i class="fas fa-spa"></i>
            </div>
            <h3>护肤品 & 彩妆</h3>
            <div class="brands">
              <span class="brand-tag">雪花秀</span>
              <span class="brand-tag">后</span>
              <span class="brand-tag">兰芝</span>
              <span class="brand-tag">悦诗风吟</span>
            </div>
            <p>韩国化妆品品质优良，价格实惠，是必买商品之一。</p>
            <div class="shopping-tip">💡 明洞、弘大是美妆购物热点</div>
          </div>

          <div class="category-card floating-card">
            <div class="category-icon">
              <i class="fas fa-tshirt"></i>
            </div>
            <h3>时尚服饰</h3>
            <div class="brands">
              <span class="brand-tag">8 Seconds</span>
              <span class="brand-tag">SPAO</span>
              <span class="brand-tag">MIXXO</span>
              <span class="brand-tag">STYLENANDA</span>
            </div>
            <p>韩式时尚风格独特，价格合理，深受年轻人喜爱。</p>
            <div class="shopping-tip">💡 东大门是服装批发集散地</div>
          </div>

          <div class="category-card floating-card">
            <div class="category-icon">
              <i class="fas fa-cookie-bite"></i>
            </div>
            <h3>零食 & 特产</h3>
            <div class="brands">
              <span class="brand-tag">海苔</span>
              <span class="brand-tag">蜂蜜柚子茶</span>
              <span class="brand-tag">辣椒酱</span>
              <span class="brand-tag">韩式泡面</span>
            </div>
            <p>韩国零食和调料是送礼佳品，口味独特值得尝试。</p>
            <div class="shopping-tip">💡 乐天免税店、新世界百货有优惠</div>
          </div>

          <div class="category-card floating-card">
            <div class="category-icon">
              <i class="fas fa-mobile-alt"></i>
            </div>
            <h3>电子产品</h3>
            <div class="brands">
              <span class="brand-tag">三星</span>
              <span class="brand-tag">LG</span>
              <span class="brand-tag">现代</span>
              <span class="brand-tag">配件</span>
            </div>
            <p>韩国电子产品技术先进，设计时尚，价格有竞争力。</p>
            <div class="shopping-tip">💡 龙山电子市场是电子产品天堂</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Travel Tips Section -->
    <section class="travel-tips">
      <div class="container">
        <div class="section-header">
          <h2>韩国旅游小贴士</h2>
          <p>让您的韩国之旅更加顺畅愉快</p>
        </div>
        
        <div class="tips-grid">
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-passport"></i>
            </div>
            <h3>签证须知</h3>
            <p>马来西亚护照可免签入境韩国90天，护照有效期需6个月以上。</p>
          </div>
          
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-won-sign"></i>
            </div>
            <h3>货币兑换</h3>
            <p>韩元为主要货币，建议在明洞等地兑换。信用卡普及，支持银联卡。</p>
          </div>
          
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-subway"></i>
            </div>
            <h3>交通攻略</h3>
            <p>地铁系统发达，建议购买T-money卡。出租车起步费较高，地铁更经济。</p>
          </div>
          
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-language"></i>
            </div>
            <h3>语言沟通</h3>
            <p>年轻人英语普及度较高，下载翻译APP会很有帮助。基本韩语会更受欢迎。</p>
          </div>

          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-wifi"></i>
            </div>
            <h3>网络通讯</h3>
            <p>WiFi覆盖率极高，地铁站、咖啡厅都有免费WiFi。可租借WiFi蛋或买SIM卡。</p>
          </div>
          
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-thermometer-half"></i>
            </div>
            <h3>最佳季节</h3>
            <p>春季(4-6月)和秋季(9-11月)是最佳旅游季节，天气宜人，景色优美。</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content floating-card">
          <h2>准备开始您的韩国之旅？</h2>
          <p>联系我们的旅游顾问，为您量身定制完美的韩国旅游行程</p>
          <div class="cta-buttons">
            <a href="/quote/" class="pulse-btn">
              <i class="fas fa-comments"></i> 立即咨询
            </a>
            <a href="tel:+60123456789" class="magnetic-btn">
              <i class="fas fa-phone"></i> 电话咨询
            </a>
            <a href="https://wa.me/60123456789" class="magnetic-btn" target="_blank">
              <i class="fab fa-whatsapp"></i> WhatsApp
            </a>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>Sky Mirror World</h3>
          <p>专业的马来西亚出发境外旅游服务商</p>
          <div class="social-links">
            <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
            <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" aria-label="WhatsApp"><i class="fab fa-whatsapp"></i></a>
          </div>
        </div>
        <div class="footer-section">
          <h4>热门目的地</h4>
          <ul>
            <li><a href="../singapore/">新加坡</a></li>
            <li><a href="../thailand/">泰国</a></li>
            <li><a href="../japan/">日本</a></li>
            <li><a href="./">韩国</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>服务项目</h4>
          <ul>
            <li><a href="../">境外旅游</a></li>
            <li><a href="/islands/">海岛游</a></li>
            <li><a href="/mice/">MICE会奖</a></li>
            <li><a href="/mm2h/">MM2H申请</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>联系我们</h4>
          <div class="contact-info">
            <p><i class="fas fa-phone"></i> +60 12-345 6789</p>
            <p><i class="fas fa-envelope"></i> <EMAIL></p>
            <p><i class="fas fa-map-marker-alt"></i> 吉隆坡, 马来西亚</p>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2024 Sky Mirror World. 保留所有权利。</p>
        <div class="footer-links">
          <a href="/privacy/">隐私政策</a>
          <a href="/terms/">使用条款</a>
          <a href="/sitemap.xml">网站地图</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "TravelAgency",
    "name": "Sky Mirror World Korea Tours",
    "description": "马来西亚出发韩国旅游包团服务",
    "url": "https://skymirrorworld.com/outbound/korea/",
    "telephone": "+60123456789",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "MY",
      "addressLocality": "Kuala Lumpur"
    },
    "offers": [
      {
        "@type": "TouristTrip",
        "name": "首尔经典5天4晚",
        "description": "韩流发源地，体验最纯正的K-Culture和现代都市风情",
        "touristType": "Cultural Tourism",
        "offers": {
          "@type": "Offer",
          "price": "2199",
          "priceCurrency": "MYR"
        }
      },
      {
        "@type": "TouristTrip",
        "name": "济州岛4天3晚",
        "description": "韩国的夏威夷，火山岛美景与韩剧取景地",
        "touristType": "Beach Tourism",
        "offers": {
          "@type": "Offer",
          "price": "1899",
          "priceCurrency": "MYR"
        }
      }
    ]
  }
  </script>

  <script defer src="../../js/main.js"></script>
  <script defer src="../../js/product-list.js"></script>
  <script defer src="../../js/visual-effects.js"></script>
  
  <script>
    // 比较功能
    let compareList = [];
    
    function addToCompare(productId) {
      if (compareList.includes(productId)) {
        alert('该产品已在比较列表中');
        return;
      }
      
      if (compareList.length >= 3) {
        alert('最多只能比较3个产品');
        return;
      }
      
      compareList.push(productId);
      updateCompareButton();
      
      // 创建提示效果
      const toast = document.createElement('div');
      toast.className = 'toast-notification';
      toast.textContent = '已添加到比较列表';
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.remove();
      }, 3000);
    }
    
    function updateCompareButton() {
      const compareBtn = document.querySelector('.compare-btn');
      if (compareBtn) {
        compareBtn.textContent = `比较 (${compareList.length})`;
        compareBtn.style.display = compareList.length > 0 ? 'block' : 'none';
      }
    }

    // K-Culture 体验卡片交互效果
    document.addEventListener('DOMContentLoaded', function() {
      const experienceCards = document.querySelectorAll('.experience-card');
      
      experienceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-8px) scale(1.05)';
          this.style.boxShadow = '0 15px 35px rgba(255, 105, 180, 0.3)';
        });
        
        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) scale(1)';
          this.style.boxShadow = '0 8px 32px rgba(31, 38, 135, 0.37)';
        });
      });

      // 购物分类卡片特效
      const categoryCards = document.querySelectorAll('.category-card');
      
      categoryCards.forEach(card => {
        card.addEventListener('click', function() {
          this.classList.add('pulse-effect');
          setTimeout(() => {
            this.classList.remove('pulse-effect');
          }, 600);
        });
      });
    });
  </script>

  <style>
    .pulse-effect {
      animation: cardPulse 0.6s ease-out;
    }

    @keyframes cardPulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
        box-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
      }
      100% {
        transform: scale(1);
      }
    }

    .korea-hero {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .brand-tag {
      display: inline-block;
      background: linear-gradient(45deg, #ff69b4, #ff1493);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      margin: 2px;
      font-weight: 500;
    }

    .shopping-tip {
      background: rgba(255, 105, 180, 0.1);
      border-left: 3px solid #ff69b4;
      padding: 8px 12px;
      margin-top: 10px;
      border-radius: 4px;
      font-size: 14px;
      color: #333;
    }

    .experience-highlight {
      background: linear-gradient(45deg, #667eea, #764ba2);
      color: white;
      padding: 6px 12px;
      border-radius: 15px;
      font-size: 12px;
      margin-top: 10px;
      display: inline-block;
    }
  </style>

  <!-- Floating Chat System -->
    <link rel="stylesheet" href="/css/floating-chat.css">
  <script defer src="/js/floating-chat.js"></script>
  
  <script defer src="/js/main.js"></script>
  <script defer src="/js/product-list.js"></script>
  <script defer src="/js/visual-effects.js"></script>
</body>
</html>