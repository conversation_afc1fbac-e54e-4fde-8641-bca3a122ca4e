<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>泰国旅游 · Sky Mirror World - 马来西亚出发泰国包团旅游</title>
  <link rel="canonical" href="" />
  <link rel="stylesheet" href="../../css/base.css" />
  <link rel="stylesheet" href="../../css/unified-pages.css" />
  <link rel="stylesheet" href="../../css/enhanced-unified.css" />
  <link rel="stylesheet" href="../../css/enhanced-effects.css" />
  <meta name="description" content="泰国旅游：曼谷芭提雅、普吉岛、清迈、苏梅岛包团旅游。专业中文导游，透明定价，马来西亚出发。" />
  <meta property="og:title" content="泰国旅游包团 - Sky Mirror World" />
  <meta property="og:description" content="马来西亚出发泰国深度游：曼谷、普吉岛、清迈等热门目的地" />
  <meta property="og:type" content="website" />
  <meta property="og:image" content="https://images.unsplash.com/photo-1528181304800-259b08848526?w=1200&h=630&fit=crop" />
  
  <!-- Google Fonts & Font Awesome -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
</head>
<body class="gradient-bg">
  <header class="site-header">
    <div class="container">
      <a class="logo" href="../../">
        <i class="fas fa-mirror"></i>
        <span>Sky Mirror World</span>
      </a>
      <nav class="top-nav">
        <a href="../" class="active">境外旅游</a>
        <a href="../../transport/">马新用车服务</a>
        <a href="../../mice/">MICE</a>
        <a href="../../islands/">海岛游</a>
        <a href="../../mm2h/">MM2H</a>
      </nav>

      <button class="mobile-menu-toggle" aria-label="打开菜单">
        <i class="fas fa-bars"></i>
      </button>
    </div>
  </header>

  <main>
    <!-- Hero Section -->
    <section class="hero-banner thailand-hero">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="glow-text">🇹🇭 泰国深度游</h1>
          <p class="typewriter">体验泰式风情 · 马来西亚出发包团旅游</p>
          <div class="hero-stats">
            <div class="stat-item fade-in">
              <span class="counter" data-target="500">0</span>+
              <small>成功团队</small>
            </div>
            <div class="stat-item fade-in">
              <span class="counter" data-target="98">0</span>%
              <small>满意度</small>
            </div>
            <div class="stat-item fade-in">
              <span class="counter" data-target="50">0</span>+
              <small>热门景点</small>
            </div>
          </div>
          <div class="cta-buttons">
            <a href="../../quote/" class="pulse-btn">立即报价</a>
            <a href="#products" class="magnetic-btn">查看行程</a>
          </div>
        </div>
        <div class="hero-visual">
          <div class="floating-card flip-card">
            <div class="flip-card-inner">
              <div class="flip-card-front">
                <img src="https://images.unsplash.com/photo-1528181304800-259b08848526?w=400&h=300&fit=crop" alt="泰国风景" />
                <h3>泰式文化</h3>
              </div>
              <div class="flip-card-back">
                <h3>🏛️ 文化之旅</h3>
                <p>探索古老寺庙与现代都市的完美融合</p>
                <ul>
                  <li>大皇宫与玉佛寺</li>
                  <li>传统水上市场</li>
                  <li>泰式按摩体验</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="wave-divider"></div>
    </section>

    <!-- Navigation Breadcrumb -->
    <nav class="breadcrumb" aria-label="面包屑导航">
      <div class="container">
        <a href="/">首页</a>
        <i class="fas fa-chevron-right"></i>
        <a href="/outbound/">境外旅游</a>
        <i class="fas fa-chevron-right"></i>
        <span>泰国</span>
      </div>
    </nav>

    <!-- Featured Destinations -->
    <section class="destinations-grid" id="products">
      <div class="container">
        <div class="section-header">
          <h2 class="rainbow-text">热门泰国目的地</h2>
          <p>精选泰国最受欢迎的旅游城市和度假胜地</p>
        </div>

        <div class="card-grid card-grid-3">
          <!-- 曼谷芭提雅 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1528181304800-259b08848526?w=400&h=250&fit=crop" alt="曼谷芭提雅" loading="lazy" />
              <div class="card-overlay">
                <div class="destination-info">
                  <h3 class="card-title">🏛️ 曼谷 + 芭提雅</h3>
                  <p class="card-description">繁华都市与海滨度假完美结合，体验泰式文化与现代生活</p>
                  <div class="price-badge">从 RM 899 起</div>
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-tags">
                <span class="card-tag hot">热门</span>
                <span class="card-tag">文化</span>
                <span class="card-tag">5天4晚</span>
              </div>
              <div class="highlights">
                <span><i class="fas fa-map-marker-alt"></i> 大皇宫</span>
                <span><i class="fas fa-water"></i> 芭提雅海滩</span>
                <span><i class="fas fa-spa"></i> 泰式按摩</span>
              </div>
              <div class="card-actions">
                <a href="bangkok-pattaya/" class="btn btn-primary">
                  <i class="fas fa-eye"></i> 查看详情
                </a>
                <button class="btn btn-secondary" onclick="addToCompare('bangkok-pattaya')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- 普吉岛 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop" alt="普吉岛" loading="lazy" />
              <div class="card-overlay">
                <span class="price-tag">从 RM 1,299 起</span>
                <div class="card-badges">
                  <span class="badge island">海岛</span>
                  <span class="badge luxury">豪华</span>
                </div>
              </div>
            </div>
            <div class="card-content">
              <h3>普吉岛度假</h3>
              <p class="duration"><i class="fas fa-clock"></i> 6天5晚</p>
              <p class="description">泰南明珠，碧海蓝天与白沙滩的完美邂逅</p>
              <div class="highlights">
                <span><i class="fas fa-umbrella-beach"></i> 卡塔海滩</span>
                <span><i class="fas fa-anchor"></i> 跳岛游</span>
                <span><i class="fas fa-cocktail"></i> 海鲜大餐</span>
              </div>
              <div class="card-actions">
                <a href="phuket/" class="btn btn-primary">查看详情</a>
                <button class="btn btn-secondary" onclick="addToCompare('phuket')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- 清迈 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=250&fit=crop" alt="清迈" loading="lazy" />
              <div class="card-overlay">
                <span class="price-tag">从 RM 799 起</span>
                <div class="card-badges">
                  <span class="badge culture">文化</span>
                  <span class="badge mountain">山城</span>
                </div>
              </div>
            </div>
            <div class="card-content">
              <h3>清迈古城</h3>
              <p class="duration"><i class="fas fa-clock"></i> 4天3晚</p>
              <p class="description">泰北玫瑰古城，传统文化与手工艺的完美融合</p>
              <div class="highlights">
                <span><i class="fas fa-mountain"></i> 素贴山</span>
                <span><i class="fas fa-hands"></i> 手工艺村</span>
                <span><i class="fas fa-fire"></i> 夜市美食</span>
              </div>
              <div class="card-actions">
                <a href="chiang-mai/" class="btn btn-primary">查看详情</a>
                <button class="btn btn-secondary" onclick="addToCompare('chiang-mai')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- 苏梅岛 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1544531586-fbd96ceaff6c?w=400&h=250&fit=crop" alt="苏梅岛" loading="lazy" />
              <div class="card-overlay">
                <span class="price-tag">从 RM 1,599 起</span>
                <div class="card-badges">
                  <span class="badge island">海岛</span>
                  <span class="badge premium">高端</span>
                </div>
              </div>
            </div>
            <div class="card-content">
              <h3>苏梅岛奢华</h3>
              <p class="duration"><i class="fas fa-clock"></i> 7天6晚</p>
              <p class="description">泰国第三大岛，椰林摇曳的私密度假天堂</p>
              <div class="highlights">
                <span><i class="fas fa-spa"></i> 度假村SPA</span>
                <span><i class="fas fa-sailboat"></i> 私人游艇</span>
                <span><i class="fas fa-cocktail"></i> 海边酒吧</span>
              </div>
              <div class="card-actions">
                <a href="koh-samui/" class="btn btn-primary">查看详情</a>
                <button class="btn btn-secondary" onclick="addToCompare('koh-samui')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Travel Tips Section -->
    <section class="travel-tips">
      <div class="container">
        <div class="section-header">
          <h2>泰国旅游小贴士</h2>
          <p>让您的泰国之旅更加顺畅愉快</p>
        </div>
        
        <div class="tips-grid">
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-passport"></i>
            </div>
            <h3>签证须知</h3>
            <p>马来西亚护照持有者可免签入境泰国30天，确保护照有效期不少于6个月。</p>
          </div>
          
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-money-bill-wave"></i>
            </div>
            <h3>货币兑换</h3>
            <p>建议在泰国当地兑换泰铢，汇率更优。大部分地方接受刷卡，但建议准备现金。</p>
          </div>
          
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-thermometer-half"></i>
            </div>
            <h3>最佳季节</h3>
            <p>11月至3月是泰国旅游旺季，天气凉爽干燥。4月至10月为雨季，但下午雨后凉爽。</p>
          </div>
          
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-utensils"></i>
            </div>
            <h3>美食文化</h3>
            <p>泰国菜以酸辣甜为主，注意辣度。街边小食丰富但需注意卫生，建议选择人气旺的摊位。</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content floating-card">
          <h2>准备开始您的泰国之旅？</h2>
          <p>联系我们的旅游顾问，为您量身定制完美的泰国旅游行程</p>
          <div class="cta-buttons">
            <a href="/quote/" class="pulse-btn">
              <i class="fas fa-comments"></i> 立即咨询
            </a>
            <a href="tel:+60123456789" class="magnetic-btn">
              <i class="fas fa-phone"></i> 电话咨询
            </a>
            <a href="https://wa.me/60123456789" class="magnetic-btn" target="_blank">
              <i class="fab fa-whatsapp"></i> WhatsApp
            </a>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>Sky Mirror World</h3>
          <p>专业的马来西亚出发境外旅游服务商</p>
          <div class="social-links">
            <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
            <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" aria-label="WhatsApp"><i class="fab fa-whatsapp"></i></a>
          </div>
        </div>
        <div class="footer-section">
          <h4>热门目的地</h4>
          <ul>
            <li><a href="../singapore/">新加坡</a></li>
            <li><a href="./">泰国</a></li>
            <li><a href="../japan/">日本</a></li>
            <li><a href="../korea/">韩国</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>服务项目</h4>
          <ul>
            <li><a href="../">境外旅游</a></li>
            <li><a href="/islands/">海岛游</a></li>
            <li><a href="/mice/">MICE会奖</a></li>
            <li><a href="/mm2h/">MM2H申请</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>联系我们</h4>
          <div class="contact-info">
            <p><i class="fas fa-phone"></i> +60 12-345 6789</p>
            <p><i class="fas fa-envelope"></i> <EMAIL></p>
            <p><i class="fas fa-map-marker-alt"></i> 吉隆坡, 马来西亚</p>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2024 Sky Mirror World. 保留所有权利。</p>
        <div class="footer-links">
          <a href="/privacy/">隐私政策</a>
          <a href="/terms/">使用条款</a>
          <a href="/sitemap.xml">网站地图</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "TravelAgency",
    "name": "Sky Mirror World Thailand Tours",
    "description": "马来西亚出发泰国旅游包团服务",
    "url": "https://skymirrorworld.com/outbound/thailand/",
    "telephone": "+60123456789",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "MY",
      "addressLocality": "Kuala Lumpur"
    },
    "offers": [
      {
        "@type": "TouristTrip",
        "name": "曼谷芭提雅5天4晚",
        "description": "繁华都市与海滨度假完美结合",
        "touristType": "Cultural Tourism",
        "offers": {
          "@type": "Offer",
          "price": "899",
          "priceCurrency": "MYR"
        }
      },
      {
        "@type": "TouristTrip",
        "name": "普吉岛6天5晚",
        "description": "泰南明珠海岛度假",
        "touristType": "Beach Tourism",
        "offers": {
          "@type": "Offer",
          "price": "1299",
          "priceCurrency": "MYR"
        }
      }
    ]
  }
  </script>

  <script>
    // 比较功能
    let compareList = [];
    
    function addToCompare(productId) {
      if (compareList.includes(productId)) {
        alert('该产品已在比较列表中');
        return;
      }
      
      if (compareList.length >= 3) {
        alert('最多只能比较3个产品');
        return;
      }
      
      compareList.push(productId);
      updateCompareButton();
      
      // 创建提示效果
      const toast = document.createElement('div');
      toast.className = 'toast-notification';
      toast.textContent = '已添加到比较列表';
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.remove();
      }, 3000);
    }
    
    function updateCompareButton() {
      const compareBtn = document.querySelector('.compare-btn');
      if (compareBtn) {
        compareBtn.textContent = `比较 (${compareList.length})`;
        compareBtn.style.display = compareList.length > 0 ? 'block' : 'none';
      }
    }
  </script>

  <!-- Floating Chat System -->
  <link rel="stylesheet" href="/css/floating-chat.css">
  <script defer src="/js/floating-chat.js"></script>
  
  <script defer src="/js/main.js"></script>
  <script defer src="/js/product-list.js"></script>
  <script defer src="/js/visual-effects.js"></script>
</body>
</html>