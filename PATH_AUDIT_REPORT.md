---
type: "manual"
---

# 路径引用审计报告

## 审计范围
以 `index.html` 为入口进行全项目路径引用排查

## 发现的问题
1. **混合使用相对和根相对路径**：不同层级页面使用不同相对路径深度
2. **缺失文件**：
   - `/assets/og-cover.webp` (Open Graph 图片)
   - `/privacy/` 和 `/terms/` 页面
   - `/islands/langkawi/` 子页面（footer链接指向但不存在）

## 修复策略：统一根相对路径
将所有本地资源改为根相对路径：
- CSS: `css/base.css` → `/css/base.css`
- JS: `js/main.js` → `/js/main.js`  
- Data: `data/destinations.json` → `/data/destinations.json`
- Pages: `outbound/` → `/outbound/`

## 需要修改的文件列表
- `index.html`
- `outbound/index.html`
- `outbound/singapore/index.html`
- `outbound/singapore/3d2n-classic/index.html`
- `outbound/thailand/index.html`
- `outbound/japan/index.html`
- `outbound/korea/index.html`
- `search/index.html`
- `quote/index.html`
- `transport/index.html`
- `islands/index.html`
- `mice/index.html`
- `mm2h/index.html`

## 本地预览要求
修改后必须使用静态服务器预览：
```bash
# 推荐方式
python -m http.server 8000
# 或
npx serve .
```

直接用 file:// 协议将无法正常工作。
