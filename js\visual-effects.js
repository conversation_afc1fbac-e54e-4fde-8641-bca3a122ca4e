/**
 * 高级视觉效果控制器
 * 管理页面中的各种视觉动画和交互效果
 */

class VisualEffectsController {
  constructor() {
    this.particles = [];
    this.particleCount = 50;
    this.animationId = null;
    this.scrollPosition = 0;
    
    this.init();
  }

  init() {
    this.createParticleSystem();
    this.initScrollEffects();
    this.initMagneticButtons();
    this.initTypingEffect();
    this.initScrollIndicator();
    this.initCardAnimations();
    this.initLoadingAnimations();
  }

  /**
   * 创建背景粒子系统
   */
  createParticleSystem() {
    const particleContainer = document.createElement('div');
    particleContainer.className = 'bg-particles';
    document.body.appendChild(particleContainer);

    for (let i = 0; i < this.particleCount; i++) {
      this.createParticle(particleContainer);
    }

    // 定期添加新粒子
    setInterval(() => {
      if (this.particles.length < this.particleCount) {
        this.createParticle(particleContainer);
      }
    }, 1000);
  }

  createParticle(container) {
    const particle = document.createElement('div');
    particle.className = 'particle';
    
    const size = Math.random() * 4 + 2;
    const startX = Math.random() * window.innerWidth;
    const duration = Math.random() * 10 + 15;
    const delay = Math.random() * 5;

    particle.style.cssText = `
      width: ${size}px;
      height: ${size}px;
      left: ${startX}px;
      animation-duration: ${duration}s;
      animation-delay: ${delay}s;
    `;

    container.appendChild(particle);
    this.particles.push(particle);

    // 清理完成的粒子
    setTimeout(() => {
      if (particle.parentNode) {
        particle.parentNode.removeChild(particle);
      }
      const index = this.particles.indexOf(particle);
      if (index > -1) {
        this.particles.splice(index, 1);
      }
    }, (duration + delay) * 1000);
  }

  /**
   * 初始化滚动效果
   */
  initScrollEffects() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
          
          // 为数字计数器添加动画
          if (entry.target.classList.contains('counter')) {
            this.animateCounter(entry.target);
          }
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    // 观察所有需要动画的元素
    document.querySelectorAll('.floating-card, .counter, .fade-in, .slide-up').forEach(el => {
      observer.observe(el);
    });
  }

  /**
   * 数字计数器动画
   */
  animateCounter(element) {
    const target = parseInt(element.getAttribute('data-target') || element.textContent);
    const duration = 2000;
    const step = target / (duration / 16);
    let current = 0;

    const timer = setInterval(() => {
      current += step;
      if (current >= target) {
        current = target;
        clearInterval(timer);
      }
      element.textContent = Math.floor(current);
    }, 16);
  }

  /**
   * 磁性按钮效果
   */
  initMagneticButtons() {
    document.querySelectorAll('.magnetic-btn').forEach(btn => {
      btn.addEventListener('mousemove', (e) => {
        const rect = btn.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;

        btn.style.transform = `translate(${x * 0.3}px, ${y * 0.3}px)`;
      });

      btn.addEventListener('mouseleave', () => {
        btn.style.transform = 'translate(0, 0)';
      });
    });
  }

  /**
   * 打字机效果
   */
  initTypingEffect() {
    const typewriterElements = document.querySelectorAll('.typewriter');
    
    typewriterElements.forEach(element => {
      const text = element.textContent;
      element.textContent = '';
      element.style.borderRight = '3px solid var(--accent-color)';
      
      let i = 0;
      const timer = setInterval(() => {
        if (i < text.length) {
          element.textContent += text.charAt(i);
          i++;
        } else {
          clearInterval(timer);
          // 保持光标闪烁
          setTimeout(() => {
            element.style.borderRight = '3px solid transparent';
          }, 1000);
        }
      }, 100);
    });
  }

  /**
   * 滚动进度指示器
   */
  initScrollIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'scroll-indicator';
    document.body.appendChild(indicator);

    window.addEventListener('scroll', () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.body.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      
      indicator.style.width = scrollPercent + '%';
    });
  }

  /**
   * 卡片动画效果
   */
  initCardAnimations() {
    document.querySelectorAll('.floating-card').forEach(card => {
      // 鼠标进入时的倾斜效果
      card.addEventListener('mousemove', (e) => {
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;
        
        card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(20px)`;
      });

      card.addEventListener('mouseleave', () => {
        card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
      });
    });
  }

  /**
   * 加载动画管理
   */
  initLoadingAnimations() {
    // 页面加载完成后移除加载动画
    window.addEventListener('load', () => {
      const loader = document.querySelector('.page-loader');
      if (loader) {
        loader.style.opacity = '0';
        setTimeout(() => {
          loader.remove();
        }, 300);
      }
    });
  }

  /**
   * 创建涟漪效果
   */
  createRipple(element, event) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 50%;
      transform: scale(0);
      animation: ripple 0.6s ease-out;
      pointer-events: none;
    `;

    element.appendChild(ripple);

    setTimeout(() => {
      ripple.remove();
    }, 600);
  }

  /**
   * 视差滚动效果
   */
  initParallaxEffect() {
    const parallaxElements = document.querySelectorAll('.parallax');
    
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;
      
      parallaxElements.forEach(element => {
        const rate = scrolled * -0.5;
        element.style.transform = `translateY(${rate}px)`;
      });
    });
  }

  /**
   * 动态背景生成
   */
  generateDynamicBackground() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    canvas.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      z-index: -2;
      opacity: 0.1;
    `;
    
    document.body.appendChild(canvas);

    const particles = [];
    const particleCount = 100;

    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2,
        size: Math.random() * 3 + 1
      });
    }

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particles.forEach(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;

        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.addRGBComponent(102, 126, 234, 0.6);
        ctx.fill();
      });

      requestAnimationFrame(animate);
    };

    animate();
  }

  /**
   * 清理资源
   */
  destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
    
    // 移除事件监听器
    window.removeEventListener('scroll', this.scrollHandler);
    window.removeEventListener('resize', this.resizeHandler);
  }
}

// 添加CSS动画关键帧
const animationStyles = `
  @keyframes ripple {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }

  @keyframes animate-in {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-in {
    animation: animate-in 0.6s ease-out forwards;
  }

  .fade-in {
    opacity: 0;
    transition: opacity 0.6s ease-out;
  }

  .fade-in.animate-in {
    opacity: 1;
  }

  .slide-up {
    transform: translateY(50px);
    opacity: 0;
    transition: all 0.6s ease-out;
  }

  .slide-up.animate-in {
    transform: translateY(0);
    opacity: 1;
  }
`;

// 将动画样式注入页面
const styleSheet = document.createElement('style');
styleSheet.textContent = animationStyles;
document.head.appendChild(styleSheet);

// 初始化视觉效果控制器
document.addEventListener('DOMContentLoaded', () => {
  window.visualEffects = new VisualEffectsController();
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = VisualEffectsController;
}