// Sky Mirror World - Home page rendering

/**
 * Render hot destinations on homepage
 */
async function renderHotDestinations() {
  const container = document.getElementById('hot-destinations');
  if (!container) return;
  
  try {
    // Show loading state
    container.innerHTML = '<div class="loading-placeholder">加载中...</div>';
    
    // Load destinations data
    const destinations = await loadDestinationsData();
    
    // Render destinations
    const html = destinations.map(destination => 
      window.SkyMirrorApp.components.createCard(destination)
    ).join('');
    
    container.innerHTML = html;
    
  } catch (error) {
    console.error('Failed to render destinations:', error);
    container.innerHTML = '<div class="error-message">加载失败，请稍后重试</div>';
  }
}

/**
 * Load destinations data (mock data for now)
 */
async function loadDestinationsData() {
  // In a real implementation, this would fetch from /data/destinations.json
  return new Promise(resolve => {
    setTimeout(() => {
      resolve([
        {
          title: '新加坡 3 天 2 晚经典游',
          subtitle: '马来西亚出发 · 市区精华',
          description: '鱼尾狮公园、滨海湾花园、圣淘沙名胜世界，适合亲子/初访',
          image: 'https://images.unsplash.com/photo-1525625293386-3f8f99389edd?w=400&h=300&fit=crop',
          price: 1299,
          tags: ['亲子', '市区游', '3天2晚'],
          link: 'outbound/singapore/3d2n-classic/index.html'
        },
        {
          title: '泰国普吉岛 5 天 4 晚',
          subtitle: '阳光海滩 · 水上活动',
          description: '芭东海滩、皇帝岛一日游、泰式按摩体验',
          image: 'https://images.unsplash.com/photo-1552465011-b4e21bf6e79a?w=400&h=300&fit=crop',
          price: 1899,
          tags: ['海滩', '度假', '5天4晚'],
          link: 'outbound/thailand/phuket-5d4n/index.html'
        },
        {
          title: '日本东京 6 天 5 晚',
          subtitle: '春季赏樱 · 文化体验',
          description: '新宿、浅草、东京迪士尼，体验传统与现代的完美融合',
          image: 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=400&h=300&fit=crop',
          price: 3299,
          tags: ['赏樱', '文化', '6天5晚'],
          link: 'outbound/japan/tokyo-6d5n/index.html'
        },
        {
          title: '兰卡威 4 天 3 晚',
          subtitle: '本地海岛 · 轻松度假',
          description: '珍南海滩、天空之桥、红树林生态游',
          image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop',
          price: 899,
          tags: ['海岛', '度假', '4天3晚'],
          link: 'islands/langkawi/4d3n-relaxing/index.html'
        },
        {
          title: '仙本那 5 天 4 晚潜水',
          subtitle: '世界级潜水胜地',
          description: '马布岛、卡帕莱、诗巴丹潜水，探索海底世界',
          image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop',
          price: 2199,
          tags: ['潜水', '海岛', '5天4晚'],
          link: '/islands/semporna/5d4n-diving/'
        },
        {
          title: '香港 4 天 3 晚购物游',
          subtitle: '购物天堂 · 美食之旅',
          description: '铜锣湾购物、太平山顶夜景、米其林美食体验',
          image: 'https://images.unsplash.com/photo-1536599018102-9f803c140fc1?w=400&h=300&fit=crop',
          price: 1799,
          tags: ['购物', '美食', '4天3晚'],
          link: '/outbound/hong-kong/4d3n-shopping/'
        }
      ]);
    }, 500);
  });
}

/**
 * Add interactive features to destination cards
 */
function initDestinationInteractions() {
  // Add click tracking for analytics
  document.addEventListener('click', (e) => {
    const card = e.target.closest('.destination-card');
    if (card) {
      const title = card.querySelector('h3')?.textContent;
      if (title) {
        // Track click event (in production, send to analytics)
        console.log('Destination clicked:', title);
      }
    }
  });
  
  // Add hover effects for better UX
  const style = document.createElement('style');
  style.textContent = `
    .destination-card {
      cursor: pointer;
    }
    
    .destination-card:hover .btn {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
    
    .loading-placeholder,
    .error-message {
      text-align: center;
      padding: var(--spacing-8);
      color: var(--gray-600);
      font-size: var(--font-size-lg);
    }
    
    .error-message {
      color: var(--error-color);
    }
  `;
  document.head.appendChild(style);
}

/**
 * Initialize homepage functionality
 */
function initHomePage() {
  renderHotDestinations();
  initDestinationInteractions();
  
  // Add scroll animations
  if ('IntersectionObserver' in window) {
    const observeElements = document.querySelectorAll('.usp-item, .service-card');
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.animation = 'fadeInUp 0.6s ease-out forwards';
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.1 });
    
    observeElements.forEach(el => observer.observe(el));
    
    // Add animation styles
    const animationStyle = document.createElement('style');
    animationStyle.textContent = `
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      .usp-item,
      .service-card {
        opacity: 0;
      }
    `;
    document.head.appendChild(animationStyle);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initHomePage);
} else {
  initHomePage();
}