/* Enhanced Unified Styles for Sky Mirror World */
/* 专门针对按钮、交互元素和视觉效果的美化 */

/* === ENHANCED BUTTONS === */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 14px 28px;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 140px;
  letter-spacing: 0.5px;
  text-transform: none;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* Button hover shine effect */
.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.6s ease;
}

.btn:hover::before {
  left: 100%;
}

/* Primary Button */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: 2px solid transparent;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.btn-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Secondary Button */
.btn-secondary {
  background: rgba(255, 255, 255, 0.95);
  color: #667eea;
  border: 2px solid #667eea;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
  border-color: #667eea;
}

.btn-secondary:active {
  transform: translateY(-1px);
}

/* Large Button */
.btn-large {
  padding: 18px 36px;
  font-size: 1.125rem;
  border-radius: 60px;
  min-width: 180px;
}

/* Small Button */
.btn-small {
  padding: 10px 20px;
  font-size: 0.875rem;
  border-radius: 40px;
  min-width: 100px;
}

/* Outline Button */
.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-3px);
}

/* Success Button */
.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: 2px solid transparent;
}

.btn-success:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #0d9488 0%, #047857 100%);
}

/* Warning Button */
.btn-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: 2px solid transparent;
}

.btn-warning:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(245, 158, 11, 0.4);
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
}

/* Danger Button */
.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: 2px solid transparent;
}

.btn-danger:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(239, 68, 68, 0.4);
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

/* White Button */
.btn-white {
  background: rgba(255, 255, 255, 0.95);
  color: #1f2937;
  border: 2px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.btn-white:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 1);
  border-color: rgba(255, 255, 255, 1);
}

/* Partner Badge Enhanced */
.partner-badge-enhanced {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  padding: 12px 20px;
  border-radius: 50px;
  margin: 20px 0;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 500;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Ghost Button */
.btn-ghost {
  background: transparent;
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
}

.btn-ghost:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  transform: translateY(-2px);
}

/* Icon only buttons */
.btn-icon {
  width: 50px;
  height: 50px;
  padding: 0;
  border-radius: 50%;
  min-width: unset;
}

.btn-icon i {
  font-size: 1.2rem;
}

/* Button groups */
.btn-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.btn-group .btn {
  flex: 1;
  min-width: 120px;
}

/* === ENHANCED CARDS === */
.enhanced-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 32px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.enhanced-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.enhanced-card:hover::before {
  transform: scaleX(1);
}

.enhanced-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15);
  border-color: rgba(102, 126, 234, 0.2);
}

/* === MM2H REQUIREMENTS CARDS === */
.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.age-badge {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  z-index: 1;
}

.requirement-list {
  margin-top: 20px;
}

.req-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  border-left: 4px solid #667eea;
  transition: all 0.3s ease;
}

.req-item:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateX(5px);
}

.req-item i {
  color: #667eea;
  font-size: 1.25rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.req-item strong {
  display: block;
  color: var(--gray-800);
  font-weight: 600;
  margin-bottom: 4px;
}

.req-item .card-description {
  color: var(--gray-600);
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

.preferred-tag {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 8px;
  display: inline-block;
}

.enhanced-card.preferred {
  border: 2px solid #10b981;
  box-shadow: 0 20px 60px rgba(16, 185, 129, 0.15);
}

.enhanced-card.preferred::before {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* === ENHANCED HERO SECTION === */
.enhanced-hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.enhanced-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255,255,255,0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(255,255,255,0.06) 0%, transparent 50%);
  pointer-events: none;
}

.hero-content-enhanced {
  position: relative;
  z-index: 2;
  max-width: 900px;
  padding: 0 20px;
}

.hero-title-enhanced {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  margin-bottom: 24px;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hero-subtitle-enhanced {
  font-size: 1.5rem;
  font-weight: 400;
  opacity: 0.95;
  margin-bottom: 40px;
  line-height: 1.6;
}

/* === ENHANCED STATS === */
.stats-enhanced {
  display: flex;
  justify-content: center;
  gap: 60px;
  flex-wrap: wrap;
  margin-top: 60px;
}

.stat-item-enhanced {
  text-align: center;
  min-width: 120px;
}

.stat-number-enhanced {
  display: block;
  font-size: 3rem;
  font-weight: 800;
  line-height: 1;
  margin-bottom: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.stat-label-enhanced {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* === ENHANCED SECTIONS === */
.section-enhanced {
  padding: 100px 0;
  position: relative;
}

.section-title-enhanced {
  font-size: 2.75rem;
  font-weight: 700;
  text-align: center;
  color: var(--gray-800);
  margin-bottom: 24px;
  position: relative;
  line-height: 1.3;
}

.section-title-enhanced::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
}

.section-subtitle-enhanced {
  font-size: 1.3rem;
  text-align: center;
  color: var(--gray-600);
  margin-bottom: 80px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* === ENHANCED NAVIGATION === */
.nav-enhanced {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-link-enhanced {
  color: var(--gray-700);
  text-decoration: none;
  font-weight: 500;
  padding: 12px 20px;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link-enhanced:hover,
.nav-link-enhanced.active {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

/* === ENHANCED INPUTS === */
.input-enhanced {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid var(--gray-200);
  border-radius: 50px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  outline: none;
}

.input-enhanced:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: white;
}

.input-enhanced::placeholder {
  color: var(--gray-400);
}

/* === ENHANCED BADGES === */
.badge-enhanced {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.badge-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.badge-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.badge-info {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

/* === RESPONSIVE ENHANCEMENTS === */
@media (max-width: 768px) {
  .btn {
    padding: 12px 24px;
    font-size: 0.9rem;
    min-width: 120px;
  }
  
  .btn-large {
    padding: 16px 32px;
    font-size: 1rem;
    min-width: 160px;
  }
  
  .btn-group {
    flex-direction: column;
    width: 100%;
  }
  
  .btn-group .btn {
    width: 100%;
    min-width: unset;
  }
  
  .stats-enhanced {
    gap: 40px;
  }
  
  .stat-number-enhanced {
    font-size: 2.5rem;
  }
  
  .section-title-enhanced {
    font-size: 2.25rem;
  }
  
  .section-subtitle-enhanced {
    font-size: 1.1rem;
  }
  
  .enhanced-card {
    padding: 24px;
    border-radius: 20px;
  }
}

@media (max-width: 480px) {
  .btn {
    padding: 10px 20px;
    font-size: 0.875rem;
    min-width: 100px;
  }
  
  .btn-large {
    padding: 14px 28px;
    font-size: 0.95rem;
    min-width: 140px;
  }
  
  .stats-enhanced {
    gap: 30px;
  }
  
  .stat-number-enhanced {
    font-size: 2rem;
  }
  
  .hero-subtitle-enhanced {
    font-size: 1.25rem;
  }
  
  .section-enhanced {
    padding: 80px 0;
  }
}

/* === ANIMATION ENHANCEMENTS === */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out;
}

.animate-fadeInLeft {
  animation: fadeInLeft 0.8s ease-out;
}

.animate-fadeInRight {
  animation: fadeInRight 0.8s ease-out;
}

.animate-delay-1 { animation-delay: 0.2s; }
.animate-delay-2 { animation-delay: 0.4s; }
.animate-delay-3 { animation-delay: 0.6s; }
.animate-delay-4 { animation-delay: 0.8s; }