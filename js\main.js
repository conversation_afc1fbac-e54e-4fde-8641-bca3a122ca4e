// Sky Mirror World - Main JavaScript

/**
 * Main application initialization and global utilities
 */

// Global app state
window.SkyMirrorApp = {
  config: {
    apiUrl: './api',
    version: '1.0.0',
    siteName: 'Sky Mirror World'
  },
  utils: {},
  components: {},
  data: {}
};

/**
 * Utility functions
 */
window.SkyMirrorApp.utils = {
  /**
   * Debounce function to limit function calls
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * Format price in MYR
   */
  formatPrice(price) {
    return new Intl.NumberFormat('ms-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0
    }).format(price);
  },

  /**
   * Format date in readable format
   */
  formatDate(date) {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(new Date(date));
  },

  /**
   * Sanitize HTML to prevent XSS
   */
  sanitizeHtml(str) {
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
  },

  /**
   * Simple template engine for string interpolation
   */
  template(str, data) {
    return str.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return data[key] || '';
    });
  },

  /**
   * Scroll to element smoothly
   */
  scrollTo(element, offset = 0) {
    const targetPosition = element.offsetTop - offset;
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });
  },

  /**
   * Show loading state
   */
  showLoading(element) {
    element.classList.add('loading');
  },

  /**
   * Hide loading state
   */
  hideLoading(element) {
    element.classList.remove('loading');
  },

  /**
   * Simple toast notification
   */
  showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // Add toast styles if not already present
    if (!document.querySelector('#toast-styles')) {
      const style = document.createElement('style');
      style.id = 'toast-styles';
      style.textContent = `
        .toast {
          position: fixed;
          top: 20px;
          right: 20px;
          padding: 12px 24px;
          border-radius: 8px;
          color: white;
          font-weight: 500;
          z-index: 10000;
          animation: slideIn 0.3s ease-out;
        }
        .toast-info { background-color: #2563eb; }
        .toast-success { background-color: #10b981; }
        .toast-warning { background-color: #f59e0b; }
        .toast-error { background-color: #ef4444; }
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
      `;
      document.head.appendChild(style);
    }
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
      toast.remove();
    }, 3000);
  },

  /**
   * Validate email format
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate phone number (basic)
   */
  isValidPhone(phone) {
    const phoneRegex = /^[\+]?[\d\s\-\(\)]{8,}$/;
    return phoneRegex.test(phone);
  },

  /**
   * Get URL parameters
   */
  getUrlParams() {
    return new URLSearchParams(window.location.search);
  },

  /**
   * Set URL parameter without page reload
   */
  setUrlParam(key, value) {
    const url = new URL(window.location);
    url.searchParams.set(key, value);
    window.history.pushState({}, '', url);
  }
};

/**
 * Component utilities
 */
window.SkyMirrorApp.components = {
  /**
   * Create a card component
   */
  createCard({ title, subtitle, image, price, tags, link, description }) {
    return `
      <div class="destination-card">
        ${image ? `<img src="${image}" alt="${title}" loading="lazy">` : ''}
        <div class="destination-card-content">
          <h3>${this.app.utils.sanitizeHtml(title)}</h3>
          ${subtitle ? `<p class="subtitle">${this.app.utils.sanitizeHtml(subtitle)}</p>` : ''}
          ${description ? `<p>${this.app.utils.sanitizeHtml(description)}</p>` : ''}
          ${price ? `<div class="price">起价 ${this.app.utils.formatPrice(price)}</div>` : ''}
          ${tags && tags.length ? `
            <div class="tags">
              ${tags.map(tag => `<span class="tag">${this.app.utils.sanitizeHtml(tag)}</span>`).join('')}
            </div>
          ` : ''}
          ${link ? `<a href="${link}" class="btn btn-primary">了解更多</a>` : ''}
        </div>
      </div>
    `;
  },

  app: window.SkyMirrorApp
};

/**
 * Mobile menu functionality
 */
function initMobileMenu() {
  const toggle = document.querySelector('.mobile-menu-toggle');
  const nav = document.querySelector('.top-nav');
  
  if (!toggle || !nav) return;
  
  toggle.addEventListener('click', () => {
    nav.classList.toggle('mobile-nav-open');
    toggle.classList.toggle('active');
    
    const icon = toggle.querySelector('i');
    if (icon) {
      icon.className = nav.classList.contains('mobile-nav-open') 
        ? 'fas fa-times' 
        : 'fas fa-bars';
    }
  });
  
  // Add mobile navigation styles
  if (!document.querySelector('#mobile-nav-styles')) {
    const style = document.createElement('style');
    style.id = 'mobile-nav-styles';
    style.textContent = `
      @media (max-width: 768px) {
        .top-nav {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          background-color: white;
          box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
          flex-direction: column;
          align-items: stretch;
          padding: var(--spacing-4);
          transform: translateY(-100%);
          opacity: 0;
          visibility: hidden;
          transition: all 0.3s ease;
        }
        
        .mobile-nav-open {
          display: flex !important;
          transform: translateY(0);
          opacity: 1;
          visibility: visible;
        }
        
        .mobile-nav-open a {
          padding: var(--spacing-3) 0;
          border-bottom: 1px solid var(--gray-200);
        }
        
        .mobile-nav-open a:last-child {
          border-bottom: none;
        }
      }
    `;
    document.head.appendChild(style);
  }
}

/**
 * Smooth scrolling for anchor links
 */
function initSmoothScrolling() {
  document.addEventListener('click', (e) => {
    const link = e.target.closest('a[href^="#"]');
    if (!link) return;
    
    e.preventDefault();
    const targetId = link.getAttribute('href').substring(1);
    const targetElement = document.getElementById(targetId);
    
    if (targetElement) {
      window.SkyMirrorApp.utils.scrollTo(targetElement, 80);
    }
  });
}

/**
 * Form validation utilities
 */
function initFormValidation() {
  const forms = document.querySelectorAll('form[data-validate]');
  
  forms.forEach(form => {
    form.addEventListener('submit', (e) => {
      if (!validateForm(form)) {
        e.preventDefault();
      }
    });
  });
}

function validateForm(form) {
  const fields = form.querySelectorAll('[required]');
  let isValid = true;
  
  fields.forEach(field => {
    const value = field.value.trim();
    let fieldValid = true;
    
    // Check if field is empty
    if (!value) {
      fieldValid = false;
    }
    
    // Email validation
    if (field.type === 'email' && value && !window.SkyMirrorApp.utils.isValidEmail(value)) {
      fieldValid = false;
    }
    
    // Phone validation
    if (field.type === 'tel' && value && !window.SkyMirrorApp.utils.isValidPhone(value)) {
      fieldValid = false;
    }
    
    // Update field appearance
    if (fieldValid) {
      field.classList.remove('error');
    } else {
      field.classList.add('error');
      isValid = false;
    }
  });
  
  return isValid;
}

/**
 * Image lazy loading fallback for older browsers
 */
function initLazyLoading() {
  if ('IntersectionObserver' in window) {
    const images = document.querySelectorAll('img[loading="lazy"]');
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
          }
          imageObserver.unobserve(img);
        }
      });
    });
    
    images.forEach(img => imageObserver.observe(img));
  }
}

/**
 * Search functionality
 */
function initSearch() {
  const searchForm = document.querySelector('.search');
  const searchInput = searchForm?.querySelector('input');
  
  if (!searchForm || !searchInput) return;
  
  // Add search suggestions
  let suggestions = [];
  
  // Debounced search suggestions
  const showSuggestions = window.SkyMirrorApp.utils.debounce((query) => {
    if (query.length < 2) {
      hideSuggestions();
      return;
    }
    
    // Filter suggestions based on query
    const filtered = suggestions.filter(item => 
      item.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 5);
    
    displaySuggestions(filtered);
  }, 300);
  
  searchInput.addEventListener('input', (e) => {
    showSuggestions(e.target.value);
  });
  
  searchInput.addEventListener('focus', (e) => {
    if (e.target.value.length >= 2) {
      showSuggestions(e.target.value);
    }
  });
  
  document.addEventListener('click', (e) => {
    if (!searchForm.contains(e.target)) {
      hideSuggestions();
    }
  });
  
  function displaySuggestions(items) {
    hideSuggestions();
    
    if (items.length === 0) return;
    
    const dropdown = document.createElement('div');
    dropdown.className = 'search-suggestions';
    dropdown.innerHTML = items.map(item => 
      `<div class="suggestion-item">${window.SkyMirrorApp.utils.sanitizeHtml(item)}</div>`
    ).join('');
    
    // Position dropdown
    dropdown.style.cssText = `
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid var(--gray-300);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      z-index: 1000;
      max-height: 200px;
      overflow-y: auto;
    `;
    
    // Add suggestion item styles
    dropdown.querySelectorAll('.suggestion-item').forEach(item => {
      item.style.cssText = `
        padding: var(--spacing-3) var(--spacing-4);
        cursor: pointer;
        border-bottom: 1px solid var(--gray-100);
        transition: background-color var(--transition-fast);
      `;
      
      item.addEventListener('mouseenter', () => {
        item.style.backgroundColor = 'var(--gray-50)';
      });
      
      item.addEventListener('mouseleave', () => {
        item.style.backgroundColor = 'transparent';
      });
      
      item.addEventListener('click', () => {
        searchInput.value = item.textContent;
        hideSuggestions();
        searchForm.submit();
      });
    });
    
    searchForm.style.position = 'relative';
    searchForm.appendChild(dropdown);
  }
  
  function hideSuggestions() {
    const existing = searchForm.querySelector('.search-suggestions');
    if (existing) {
      existing.remove();
    }
  }
  
  // Load search suggestions from data
  loadSearchSuggestions();
  
  async function loadSearchSuggestions() {
    try {
      // This would typically load from /data/search-suggestions.json
      suggestions = [
        '新加坡', '泰国', '日本', '香港', '台湾',
        '兰卡威', '热浪岛', '仙本那', '刁曼岛',
        'MICE', 'MM2H', '亲子游', '蜜月游', '潜水'
      ];
    } catch (error) {
      console.warn('Could not load search suggestions:', error);
    }
  }
}

/**
 * Performance monitoring
 */
function initPerformanceMonitoring() {
  // Monitor Core Web Vitals
  if ('web-vital' in window) {
    // This would integrate with a real web vitals library
    console.log('Web Vitals monitoring initialized');
  }
  
  // Monitor page load time
  window.addEventListener('load', () => {
    const loadTime = performance.now();
    console.log(`Page loaded in ${Math.round(loadTime)}ms`);
  });
}

/**
 * Error handling
 */
function initErrorHandling() {
  window.addEventListener('error', (event) => {
    console.error('JavaScript error:', event.error);
    // In production, this would send errors to monitoring service
  });
  
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
  });
}

/**
 * Initialize the application
 */
function init() {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
    return;
  }
  
  try {
    initMobileMenu();
    initSmoothScrolling();
    initFormValidation();
    initLazyLoading();
    initSearch();
    initPerformanceMonitoring();
    initErrorHandling();
    
    console.log('Sky Mirror World app initialized');
  } catch (error) {
    console.error('Failed to initialize app:', error);
  }
}

// Initialize when script loads
init();