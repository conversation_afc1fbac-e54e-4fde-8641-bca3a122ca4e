<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>智能对话行程报价 | Sky Mirror World</title>
  <link rel="canonical" href="../quote/" />
  <link rel="stylesheet" href="../css/base.css" />
  <link rel="stylesheet" href="../css/quote.css" />
  <link rel="stylesheet" href="../css/enhanced-effects.css" />
  <link rel="stylesheet" href="../css/quote-enhanced.css" />
  <meta name="description" content="使用AI智能对话获取个性化旅游行程报价。简单对话即可定制专属行程，马来西亚出发境外旅游和海岛游。" />
  <meta property="og:title" content="智能对话行程报价 | Sky Mirror World" />
  <meta property="og:description" content="AI智能对话，个性化定制旅游行程，快速获取专业报价" />
  <meta property="og:type" content="website" />
  <meta property="og:image" content="/assets/quote-og.webp" />
  <meta name="twitter:card" content="summary_large_image" />
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
</head>
<body>
  <header class="site-header">
    <div class="container">
      <a class="logo" href="../">
        <i class="fas fa-mirror"></i>
        <span>Sky Mirror World</span>
      </a>
      <nav class="top-nav">
        <a href="../outbound/">境外旅游</a>
        <a href="../transport/">马新用车服务</a>
        <a href="../mice/">MICE</a>
        <a href="../islands/">海岛游</a>
        <a href="../mm2h/">MM2H</a>
      </nav>

      <button class="mobile-menu-toggle" aria-label="打开菜单">
        <i class="fas fa-bars"></i>
      </button>
    </div>
  </header>

  <!-- Breadcrumb -->
  <nav class="breadcrumb">
    <div class="container">
      <ol>
        <li><a href="../">首页</a></li>
        <li>智能报价</li>
      </ol>
    </div>
  </nav>

  <main class="quote-page">
    <!-- Page Header -->
    <section class="quote-header gradient-bg">
      <div class="container">
        <div class="header-content">
          <h1 class="glow-text"><i class="fas fa-robot"></i> AI 智能对话行程报价</h1>
          <p class="header-subtitle typewriter">通过简单对话，获取个性化旅游行程和专业报价</p>
          
          <div class="stats-display">
            <div class="stat-item fade-in">
              <span class="counter" data-target="10000">0</span>+
              <small>成功报价</small>
            </div>
            <div class="stat-item fade-in">
              <span class="counter" data-target="98">0</span>%
              <small>满意度</small>
            </div>
            <div class="stat-item fade-in">
              <span class="counter" data-target="24">0</span>h
              <small>在线服务</small>
            </div>
          </div>

          <div class="features-grid">
            <div class="feature floating-card slide-up">
              <div class="feature-icon">
                <i class="fas fa-comments"></i>
              </div>
              <h3>智能对话</h3>
              <p>自然语言交流，理解您的真实需求</p>
            </div>
            <div class="feature floating-card slide-up">
              <div class="feature-icon">
                <i class="fas fa-magic"></i>
              </div>
              <h3>个性定制</h3>
              <p>根据偏好和预算，定制专属行程</p>
            </div>
            <div class="feature floating-card slide-up">
              <div class="feature-icon">
                <i class="fas fa-calculator"></i>
              </div>
              <h3>即时报价</h3>
              <p>实时计算价格，透明公开无隐藏</p>
            </div>
            <div class="feature floating-card slide-up">
              <div class="feature-icon">
                <i class="fas fa-file-pdf"></i>
              </div>
              <h3>PDF导出</h3>
              <p>专业报价单，一键导出分享</p>
            </div>
          </div>
        </div>
      </div>
      <div class="wave-divider"></div>
    </section>

    <!-- Quote Interface -->
    <section class="quote-interface">
      <div class="container">
        <div class="quote-layout">
          <!-- Chat Panel -->
          <div class="chat-panel floating-card">
            <div class="chat-header">
              <div class="chat-title">
                <div class="ai-avatar">
                  <i class="fas fa-robot"></i>
                </div>
                <div class="chat-info">
                  <h3>Sky Travel AI</h3>
                  <div class="chat-status">
                    <span class="status-indicator online pulse"></span>
                    <span>智能顾问在线</span>
                  </div>
                </div>
              </div>
              <div class="chat-actions">
                <button class="action-btn" onclick="clearChat()" title="清空对话">
                  <i class="fas fa-trash"></i>
                </button>
                <button class="action-btn" onclick="exportChat()" title="导出对话">
                  <i class="fas fa-download"></i>
                </button>
              </div>
            </div>
            
            <div class="chat-messages" id="chat-messages">
              <div class="message bot-message fade-in">
                <div class="message-avatar">
                  <div class="avatar-image">
                    <i class="fas fa-robot"></i>
                  </div>
                </div>
                <div class="message-content">
                  <div class="message-bubble">
                    <p>🎉 您好！我是Sky Mirror World的智能旅游顾问小Sky。</p>
                    <p>我可以帮您定制专属的旅游行程并提供实时报价。无论是境外旅游还是海岛度假，让我们一起规划您的完美旅程！</p>
                    <div class="message-time">刚刚</div>
                  </div>
                  <div class="quick-replies">
                    <div class="quick-replies-label">热门目的地</div>
                    <button class="quick-reply magnetic-btn" data-reply="新加坡">🇸🇬 新加坡</button>
                    <button class="quick-reply magnetic-btn" data-reply="泰国">🇹🇭 泰国</button>
                    <button class="quick-reply magnetic-btn" data-reply="日本">🇯🇵 日本</button>
                    <button class="quick-reply magnetic-btn" data-reply="韩国">🇰🇷 韩国</button>
                    <button class="quick-reply magnetic-btn" data-reply="兰卡威">🏝️ 兰卡威</button>
                    <button class="quick-reply magnetic-btn" data-reply="热浪岛">🐠 热浪岛</button>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="chat-input">
              <div class="input-container floating-card">
                <div class="input-wrapper">
                  <input type="text" id="user-input" placeholder="告诉我您的旅游想法，比如：想去日本看樱花..." />
                  <div class="input-actions">
                    <button class="emoji-btn" onclick="toggleEmojiPanel()" title="表情">
                      <i class="fas fa-smile"></i>
                    </button>
                    <button class="voice-btn" onclick="toggleVoiceInput()" title="语音输入">
                      <i class="fas fa-microphone"></i>
                    </button>
                  </div>
                </div>
                <button id="send-button" type="button" class="pulse-btn">
                  <i class="fas fa-paper-plane"></i>
                </button>
              </div>
              
              <div class="input-suggestions">
                <div class="suggestions-header">
                  <i class="fas fa-lightbulb"></i>
                  <span>试试这些问题</span>
                </div>
                <div class="suggestions-grid">
                  <button class="suggestion magnetic-btn" data-text="我想去新加坡3天2晚，预算3000左右">
                    <i class="fas fa-building"></i> 新加坡3天2晚
                  </button>
                  <button class="suggestion magnetic-btn" data-text="适合一家四口的日本行程，包含迪士尼">
                    <i class="fas fa-family"></i> 亲子游日本
                  </button>
                  <button class="suggestion magnetic-btn" data-text="想要海岛度假，兰卡威或热浪岛">
                    <i class="fas fa-umbrella-beach"></i> 海岛度假
                  </button>
                  <button class="suggestion magnetic-btn" data-text="韩国首尔购物美食5天，包含K-POP体验">
                    <i class="fas fa-shopping-bag"></i> 韩国购物游
                  </button>
                </div>
              </div>
              
              <div class="typing-indicator" id="typing-indicator" style="display: none;">
                <div class="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <span>小Sky正在思考中...</span>
              </div>
            </div>
          </div>

          <!-- Quote Panel -->
          <div class="quote-panel floating-card">
            <div class="quote-header">
              <div class="quote-title">
                <h3><i class="fas fa-file-invoice"></i> 智能报价单</h3>
                <div class="quote-meta">
                  <span class="quote-id">ID: QT-<span id="quote-number">001</span></span>
                  <span class="quote-date" id="quote-date"></span>
                </div>
              </div>
              <div class="quote-status">
                <span class="status-badge draft" id="quote-status">
                  <i class="fas fa-edit"></i> 草稿
                </span>
                <div class="quote-actions">
                  <button class="action-btn" onclick="saveQuote()" title="保存报价单">
                    <i class="fas fa-save"></i>
                  </button>
                  <button class="action-btn" onclick="shareQuote()" title="分享报价单">
                    <i class="fas fa-share"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <div class="quote-content" id="quote-content">
              <div class="quote-placeholder">
                <div class="placeholder-animation">
                  <div class="floating-icons">
                    <i class="fas fa-plane"></i>
                    <i class="fas fa-map-marked-alt"></i>
                    <i class="fas fa-calendar-alt"></i>
                    <i class="fas fa-users"></i>
                  </div>
                </div>
                <h4>开始您的智能旅游规划</h4>
                <p>✨ 告诉我您的旅游想法，我会实时为您生成：</p>
                <ul class="placeholder-features">
                  <li><i class="fas fa-route"></i> 详细行程安排</li>
                  <li><i class="fas fa-calculator"></i> 精准价格计算</li>
                  <li><i class="fas fa-bed"></i> 酒店住宿推荐</li>
                  <li><i class="fas fa-utensils"></i> 美食餐厅建议</li>
                </ul>
                <div class="start-chat-btn">
                  <button class="pulse-btn" onclick="focusChatInput()">
                    <i class="fas fa-comments"></i> 开始对话
                  </button>
                </div>
              </div>
            </div>
            
            <div class="quote-actions" id="quote-actions" style="display: none;">
              <button class="btn btn-secondary" id="edit-quote">
                <i class="fas fa-edit"></i>
                修改需求
              </button>
              <button class="btn btn-primary" id="export-quote">
                <i class="fas fa-download"></i>
                导出PDF
              </button>
              <button class="btn btn-primary" id="submit-quote">
                <i class="fas fa-paper-plane"></i>
                提交询价
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- How it Works -->
    <section class="how-it-works">
      <div class="container">
        <h2>如何使用智能报价</h2>
        <div class="steps">
          <div class="step">
            <div class="step-number">1</div>
            <h3>告诉我们您的需求</h3>
            <p>目的地、时间、人数、预算、兴趣偏好等</p>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <h3>AI智能推荐行程</h3>
            <p>基于您的需求，智能生成个性化行程方案</p>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <h3>获取详细报价</h3>
            <p>包含住宿、交通、景点、餐食等详细费用</p>
          </div>
          <div class="step">
            <div class="step-number">4</div>
            <h3>确认并预订</h3>
            <p>满意后提交询价，专业顾问跟进服务</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Features -->
    <section class="quote-features">
      <div class="container">
        <h2>智能报价的优势</h2>
        <div class="features-grid">
          <div class="feature-card">
            <i class="fas fa-brain"></i>
            <h3>AI智能推荐</h3>
            <p>基于大数据和机器学习，推荐最适合您的行程</p>
          </div>
          <div class="feature-card">
            <i class="fas fa-clock"></i>
            <h3>24/7 即时服务</h3>
            <p>随时随地获取报价，不受时间地点限制</p>
          </div>
          <div class="feature-card">
            <i class="fas fa-user-cog"></i>
            <h3>个性化定制</h3>
            <p>根据您的偏好和需求，量身定制专属行程</p>
          </div>
          <div class="feature-card">
            <i class="fas fa-dollar-sign"></i>
            <h3>透明定价</h3>
            <p>详细费用清单，无隐藏费用，价格公开透明</p>
          </div>
          <div class="feature-card">
            <i class="fas fa-mobile-alt"></i>
            <h3>多平台支持</h3>
            <p>支持手机、平板、电脑，随时随地使用</p>
          </div>
          <div class="feature-card">
            <i class="fas fa-headset"></i>
            <h3>人工客服支持</h3>
            <p>AI不能解决的问题，专业人工客服跟进</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials -->
    <section class="testimonials">
      <div class="container">
        <h2>用户评价</h2>
        <div class="testimonials-grid">
          <div class="testimonial">
            <div class="testimonial-content">
              <p>"太方便了！几分钟就得到了详细的新加坡行程报价，比传统咨询快多了。"</p>
            </div>
            <div class="testimonial-author">
              <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face" alt="用户头像">
              <div>
                <strong>林小姐</strong>
                <span>吉隆坡</span>
              </div>
            </div>
          </div>
          <div class="testimonial">
            <div class="testimonial-content">
              <p>"AI推荐的日本行程很符合我们家庭的需求，价格也很合理，已经预订了！"</p>
            </div>
            <div class="testimonial-author">
              <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face" alt="用户头像">
              <div>
                <strong>陈先生</strong>
                <span>槟城</span>
              </div>
            </div>
          </div>
          <div class="testimonial">
            <div class="testimonial-content">
              <p>"对话很自然，就像和真人顾问聊天一样，而且响应速度超快。"</p>
            </div>
            <div class="testimonial-author">
              <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face" alt="用户头像">
              <div>
                <strong>王女士</strong>
                <span>新山</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>Sky Mirror World</h3>
          <p>专业的马来西亚出发境外旅游服务商</p>
          <div class="social-links">
            <a href="#" aria-label="微信"><i class="fab fa-weixin"></i></a>
            <a href="#" aria-label="WhatsApp"><i class="fab fa-whatsapp"></i></a>
            <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
          </div>
        </div>
        <div class="footer-section">
          <h4>热门目的地</h4>
          <ul>
            <li><a href="../outbound/singapore/">新加坡</a></li>
            <li><a href="../outbound/thailand/">泰国</a></li>
            <li><a href="../outbound/japan/">日本</a></li>
            <li><a href="../islands/langkawi/">兰卡威</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>服务项目</h4>
          <ul>
            <li><a href="../outbound/">境外旅游</a></li>
            <li><a href="../islands/">海岛游</a></li>
            <li><a href="../mice/">MICE会奖</a></li>
            <li><a href="../mm2h/">MM2H长居</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>联系我们</h4>
          <p><i class="fas fa-phone"></i> +60 3-1234 5678</p>
          <p><i class="fas fa-envelope"></i> <EMAIL></p>
          <p><i class="fas fa-map-marker-alt"></i> 吉隆坡, 马来西亚</p>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2024 Sky Mirror World Tour Sdn Bhd. All rights reserved.</p>
        <div class="footer-links">
          <a href="../privacy/">隐私政策</a>
          <a href="../terms/">使用条款</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- JSON-LD Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Sky Mirror World 智能报价系统",
    "description": "AI驱动的智能旅游行程报价系统，个性化定制马来西亚出发的境外旅游行程",
    "url": "https://skymirrorworld.com/quote/",
    "applicationCategory": "TravelApplication",
    "operatingSystem": "Web",
    "browserRequirements": "Requires JavaScript",
    "provider": {
      "@type": "TravelAgency",
      "name": "Sky Mirror World Tour"
    },
    "featureList": [
      "AI智能对话",
      "个性化行程推荐",
      "即时报价计算",
      "PDF报价单导出",
      "多语言支持"
    ]
  }
  </script>

  <script defer src="../js/main.js"></script>
  <script defer src="../js/quote-chat.js"></script>
  <script defer src="../js/visual-effects.js"></script>
  <script defer src="../js/quote-enhanced.js"></script>
</body>
</html>