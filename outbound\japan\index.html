<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>日本旅游 · Sky Mirror World - 马来西亚出发日本包团旅游</title>
  <link rel="canonical" href="/outbound/japan/" />
  <link rel="stylesheet" href="/css/base.css" />
  <link rel="stylesheet" href="/css/unified-pages.css" />
  <link rel="stylesheet" href="/css/enhanced-unified.css" />
  <link rel="stylesheet" href="/css/enhanced-effects.css" />
  <meta name="description" content="日本旅游：东京大阪、京都奈良、北海道、冲绳包团旅游。专业中文导游，透明定价，马来西亚出发。" />
  <meta property="og:title" content=  <link rel="stylesheet" href="/css/floating-chat.css">
  <script defer src="/js/floating-chat.js"></script>
  
  <script defer src="/js/main.js"></script>
  <script defer src="/js/product-list.js"></script>
  <script defer src="/js/visual-effects.js"></script>本旅游包团 - Sky Mirror World" />
  <meta property="og:description" content="马来西亚出发日本深度游：东京、大阪、京都、北海道等热门目的地" />
  <meta property="og:type" content="website" />
  <meta property="og:image" content="https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?w=1200&h=630&fit=crop" />
  
  <!-- Google Fonts & Font Awesome -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
</head>
<body class="gradient-bg">
  <header class="site-header">
    <div class="container">
      <a class="logo" href="/">
        <i class="fas fa-mirror"></i>
        <span>Sky Mirror World</span>
      </a>
      <nav class="top-nav">
        <a href="/outbound/" class="active">境外旅游</a>
        <a href="/transport/">马新用车服务</a>
        <a href="/mice/">MICE</a>
        <a href="/islands/">海岛游</a>
        <a href="/mm2h/">MM2H</a>
      </nav>

      <button class="mobile-menu-toggle" aria-label="打开菜单">
        <i class="fas fa-bars"></i>
      </button>
    </div>
  </header>

  <main>
    <!-- Hero Section -->
    <section class="hero-banner japan-hero">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="glow-text">🇯🇵 日本深度游</h1>
          <p class="typewriter">体验和风文化 · 马来西亚出发包团旅游</p>
          <div class="hero-stats">
            <div class="stat-item fade-in">
              <span class="counter" data-target="800">0</span>+
              <small>成功团队</small>
            </div>
            <div class="stat-item fade-in">
              <span class="counter" data-target="99">0</span>%
              <small>满意度</small>
            </div>
            <div class="stat-item fade-in">
              <span class="counter" data-target="100">0</span>+
              <small>热门景点</small>
            </div>
          </div>
          <div class="cta-buttons">
            <a href="/quote/" class="pulse-btn">立即报价</a>
            <a href="#products" class="magnetic-btn">查看行程</a>
          </div>
        </div>
        <div class="hero-visual">
          <div class="floating-card flip-card">
            <div class="flip-card-inner">
              <div class="flip-card-front">
                <img src="https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?w=400&h=300&fit=crop" alt="日本风景" />
                <h3>和风文化</h3>
              </div>
              <div class="flip-card-back">
                <h3>🏯 文化之旅</h3>
                <p>探索古老传统与现代科技的完美融合</p>
                <ul>
                  <li>古寺神社巡礼</li>
                  <li>和服体验</li>
                  <li>温泉料理</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="wave-divider"></div>
    </section>

    <!-- Navigation Breadcrumb -->
    <nav class="breadcrumb" aria-label="面包屑导航">
      <div class="container">
        <a href="/">首页</a>
        <i class="fas fa-chevron-right"></i>
        <a href="/outbound/">境外旅游</a>
        <i class="fas fa-chevron-right"></i>
        <span>日本</span>
      </div>
    </nav>

    <!-- Featured Destinations -->
    <section class="destinations-grid" id="products">
      <div class="container">
        <div class="section-header">
          <h2 class="rainbow-text">热门日本目的地</h2>
          <p>精选日本最受欢迎的旅游城市和文化胜地</p>
        </div>

        <div class="card-grid card-grid-3">
          <!-- 东京大阪 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=400&h=250&fit=crop" alt="东京大阪" loading="lazy" />
              <div class="card-overlay">
                <div class="destination-info">
                  <h3 class="card-title">🏙️ 东京 + 大阪</h3>
                  <p class="card-description">关东关西双城记，现代都市与传统文化的完美碰撞</p>
                  <div class="price-badge">从 RM 2,899 起</div>
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-tags">
                <span class="card-tag hot">热门</span>
                <span class="card-tag">文化</span>
                <span class="card-tag">7天6晚</span>
              </div>
              <div class="highlights">
                <span><i class="fas fa-building"></i> 东京塔</span>
                <span><i class="fas fa-fish"></i> 筑地市场</span>
                <span><i class="fas fa-castle"></i> 大阪城</span>
              </div>
              <div class="card-actions">
                <a href="tokyo-osaka/" class="btn btn-primary">
                  <i class="fas fa-eye"></i> 查看详情
                </a>
                <button class="btn btn-secondary" onclick="addToCompare('tokyo-osaka')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- 京都奈良 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=250&fit=crop" alt="京都奈良" loading="lazy" />
              <div class="card-overlay">
                <div class="destination-info">
                  <h3 class="card-title">🏯 京都 + 奈良</h3>
                  <p class="card-description">千年古都，感受最纯正的日本传统文化</p>
                  <div class="price-badge">从 RM 2,599 起</div>
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-tags">
                <span class="card-tag">文化</span>
                <span class="card-tag">古都</span>
                <span class="card-tag">6天5晚</span>
              </div>
              <div class="highlights">
                <span><i class="fas fa-torii-gate"></i> 金阁寺</span>
                <span><i class="fas fa-deer"></i> 奈良公园</span>
                <span><i class="fas fa-mountain"></i> 伏见稻荷</span>
              </div>
              <div class="card-actions">
                <a href="kyoto-nara/" class="btn btn-primary">
                  <i class="fas fa-eye"></i> 查看详情
                </a>
                <button class="btn btn-secondary" onclick="addToCompare('kyoto-nara')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- 北海道 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1578968819795-11882ff6c073?w=400&h=250&fit=crop" alt="北海道" loading="lazy" />
              <div class="card-overlay">
                <div class="destination-info">
                  <h3 class="card-title">❄️ 北海道浪漫</h3>
                  <p class="card-description">雪国风情，浪漫小樽与札幌美食之旅</p>
                  <div class="price-badge">从 RM 3,599 起</div>
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-tags">
                <span class="card-tag">自然</span>
                <span class="card-tag premium">高端</span>
                <span class="card-tag">8天7晚</span>
              </div>
              <div class="highlights">
                <span><i class="fas fa-snowflake"></i> 雪季美景</span>
                <span><i class="fas fa-fish"></i> 海鲜料理</span>
                <span><i class="fas fa-hot-tub"></i> 温泉体验</span>
              </div>
              <div class="card-actions">
                <a href="hokkaido/" class="btn btn-primary">
                  <i class="fas fa-eye"></i> 查看详情
                </a>
                <button class="btn btn-secondary" onclick="addToCompare('hokkaido')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- 冲绳岛 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=250&fit=crop" alt="冲绳" loading="lazy" />
              <div class="card-overlay">
                <div class="destination-info">
                  <h3 class="card-title">🏝️ 冲绳海岛</h3>
                  <p class="card-description">日本的夏威夷，碧海蓝天的热带度假天堂</p>
                  <div class="price-badge">从 RM 2,299 起</div>
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-tags">
                <span class="card-tag">海岛</span>
                <span class="card-tag">休闲</span>
                <span class="card-tag">5天4晚</span>
              </div>
              <div class="highlights">
                <span><i class="fas fa-umbrella-beach"></i> 海滩度假</span>
                <span><i class="fas fa-mask"></i> 琉球文化</span>
                <span><i class="fas fa-cocktail"></i> 海岛美食</span>
              </div>
              <div class="card-actions">
                <a href="okinawa/" class="btn btn-primary">
                  <i class="fas fa-eye"></i> 查看详情
                </a>
                <button class="btn btn-secondary" onclick="addToCompare('okinawa')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- 富士山河口湖 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1490806843957-31f4c9a91c65?w=400&h=250&fit=crop" alt="富士山" loading="lazy" />
              <div class="card-overlay">
                <div class="destination-info">
                  <h3 class="card-title">🗻 富士山河口湖</h3>
                  <p class="card-description">日本象征，神圣的富士山与美丽的河口湖</p>
                  <div class="price-badge">从 RM 2,199 起</div>
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-tags">
                <span class="card-tag">自然</span>
                <span class="card-tag">景观</span>
                <span class="card-tag">5天4晚</span>
              </div>
              <div class="highlights">
                <span><i class="fas fa-mountain"></i> 富士山</span>
                <span><i class="fas fa-water"></i> 河口湖</span>
                <span><i class="fas fa-camera"></i> 观景台</span>
              </div>
              <div class="card-actions">
                <a href="mt-fuji/" class="btn btn-primary">
                  <i class="fas fa-eye"></i> 查看详情
                </a>
                <button class="btn btn-secondary" onclick="addToCompare('mt-fuji')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>

          <!-- 广岛宫岛 -->
          <div class="enhanced-card image-card">
            <div class="card-image">
              <img src="https://images.unsplash.com/photo-1528360983277-13d401cdc186?w=400&h=250&fit=crop" alt="广岛" loading="lazy" />
              <div class="card-overlay">
                <div class="destination-info">
                  <h3 class="card-title">⛩️ 广岛 + 宫岛</h3>
                  <p class="card-description">历史见证与神圣鸟居，和平与美景的交融</p>
                  <div class="price-badge">从 RM 2,799 起</div>
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-tags">
                <span class="card-tag">历史</span>
                <span class="card-tag">文化</span>
                <span class="card-tag">6天5晚</span>
              </div>
              <div class="highlights">
                <span><i class="fas fa-dove"></i> 和平公园</span>
                <span><i class="fas fa-torii-gate"></i> 严岛神社</span>
                <span><i class="fas fa-utensils"></i> 广岛烧</span>
              </div>
              <div class="card-actions">
                <a href="hiroshima/" class="btn btn-primary">
                  <i class="fas fa-eye"></i> 查看详情
                </a>
                <button class="btn btn-secondary" onclick="addToCompare('hiroshima')">
                  <i class="fas fa-plus"></i> 比较
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Season Guide Section -->
    <section class="season-guide">
      <div class="container">
        <div class="section-header">
          <h2>🌸 日本四季旅游指南</h2>
          <p>每个季节都有不同的美景和体验</p>
        </div>
        
        <div class="seasons-grid">
          <div class="season-card floating-card">
            <div class="season-icon">
              <i class="fas fa-seedling"></i>
            </div>
            <h3>春季 (3-5月)</h3>
            <div class="season-highlight">🌸 樱花季</div>
            <p>最佳赏樱时期，从南到北依次绽放。东京、京都、大阪是热门赏樱地点。</p>
            <div class="season-temp">平均气温：10-20°C</div>
          </div>
          
          <div class="season-card floating-card">
            <div class="season-icon">
              <i class="fas fa-sun"></i>
            </div>
            <h3>夏季 (6-8月)</h3>
            <div class="season-highlight">🎆 祭典季</div>
            <p>夏日祭典、花火大会，体验日本传统节日文化。北海道是避暑胜地。</p>
            <div class="season-temp">平均气温：25-35°C</div>
          </div>
          
          <div class="season-card floating-card">
            <div class="season-icon">
              <i class="fas fa-leaf"></i>
            </div>
            <h3>秋季 (9-11月)</h3>
            <div class="season-highlight">🍁 红叶季</div>
            <p>红叶满山，京都、奈良、富士山区域是最佳观赏地点。天气凉爽舒适。</p>
            <div class="season-temp">平均气温：10-25°C</div>
          </div>
          
          <div class="season-card floating-card">
            <div class="season-icon">
              <i class="fas fa-snowflake"></i>
            </div>
            <h3>冬季 (12-2月)</h3>
            <div class="season-highlight">⛷️ 滑雪季</div>
            <p>雪景如画，北海道滑雪，温泉体验。冬季点灯活动营造浪漫氛围。</p>
            <div class="season-temp">平均气温：0-10°C</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Travel Tips Section -->
    <section class="travel-tips">
      <div class="container">
        <div class="section-header">
          <h2>日本旅游小贴士</h2>
          <p>让您的日本之旅更加顺畅愉快</p>
        </div>
        
        <div class="tips-grid">
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-passport"></i>
            </div>
            <h3>签证须知</h3>
            <p>马来西亚护照需要签证，建议提前1个月申请。单次入境可停留15-90天。</p>
          </div>
          
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-yen-sign"></i>
            </div>
            <h3>货币兑换</h3>
            <p>日元现金为主，建议在当地7-Eleven ATM取现。交通卡、信用卡普及程度高。</p>
          </div>
          
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-train"></i>
            </div>
            <h3>交通攻略</h3>
            <p>JR Pass是长途旅行的好选择。东京、大阪地铁发达，购买一日券更实惠。</p>
          </div>
          
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-bow"></i>
            </div>
            <h3>文化礼仪</h3>
            <p>日本注重礼貌，进门脱鞋，电车内保持安静，不要边走边吃。鞠躬是常见礼仪。</p>
          </div>

          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-wifi"></i>
            </div>
            <h3>网络通讯</h3>
            <p>租借WiFi蛋或购买SIM卡。很多地方有免费WiFi，包括便利店和车站。</p>
          </div>
          
          <div class="tip-card floating-card">
            <div class="tip-icon">
              <i class="fas fa-utensils"></i>
            </div>
            <h3>美食文化</h3>
            <p>拉面、寿司、天妇罗必尝。用餐前说"いただきます"，用餐后说"ごちそうさまでした"。</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content floating-card">
          <h2>准备开始您的日本之旅？</h2>
          <p>联系我们的旅游顾问，为您量身定制完美的日本旅游行程</p>
          <div class="cta-buttons">
            <a href="/quote/" class="pulse-btn">
              <i class="fas fa-comments"></i> 立即咨询
            </a>
            <a href="tel:+60123456789" class="magnetic-btn">
              <i class="fas fa-phone"></i> 电话咨询
            </a>
            <a href="https://wa.me/60123456789" class="magnetic-btn" target="_blank">
              <i class="fab fa-whatsapp"></i> WhatsApp
            </a>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>Sky Mirror World</h3>
          <p>专业的马来西亚出发境外旅游服务商</p>
          <div class="social-links">
            <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
            <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" aria-label="WhatsApp"><i class="fab fa-whatsapp"></i></a>
          </div>
        </div>
        <div class="footer-section">
          <h4>热门目的地</h4>
          <ul>
            <li><a href="../singapore/">新加坡</a></li>
            <li><a href="../thailand/">泰国</a></li>
            <li><a href="./">日本</a></li>
            <li><a href="../korea/">韩国</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>服务项目</h4>
          <ul>
            <li><a href="../">境外旅游</a></li>
            <li><a href="/islands/">海岛游</a></li>
            <li><a href="/mice/">MICE会奖</a></li>
            <li><a href="/mm2h/">MM2H申请</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>联系我们</h4>
          <div class="contact-info">
            <p><i class="fas fa-phone"></i> +60 12-345 6789</p>
            <p><i class="fas fa-envelope"></i> <EMAIL></p>
            <p><i class="fas fa-map-marker-alt"></i> 吉隆坡, 马来西亚</p>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2024 Sky Mirror World. 保留所有权利。</p>
        <div class="footer-links">
          <a href="/privacy/">隐私政策</a>
          <a href="/terms/">使用条款</a>
          <a href="/sitemap.xml">网站地图</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "TravelAgency",
    "name": "Sky Mirror World Japan Tours",
    "description": "马来西亚出发日本旅游包团服务",
    "url": "https://skymirrorworld.com/outbound/japan/",
    "telephone": "+60123456789",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "MY",
      "addressLocality": "Kuala Lumpur"
    },
    "offers": [
      {
        "@type": "TouristTrip",
        "name": "东京大阪7天6晚",
        "description": "关东关西双城记，现代都市与传统文化的完美碰撞",
        "touristType": "Cultural Tourism",
        "offers": {
          "@type": "Offer",
          "price": "2899",
          "priceCurrency": "MYR"
        }
      },
      {
        "@type": "TouristTrip",
        "name": "京都奈良6天5晚",
        "description": "千年古都，感受最纯正的日本传统文化",
        "touristType": "Cultural Tourism",
        "offers": {
          "@type": "Offer",
          "price": "2599",
          "priceCurrency": "MYR"
        }
      }
    ]
  }
  </script>

  <script defer src="../../js/main.js"></script>
  <script defer src="../../js/product-list.js"></script>
  <script defer src="../../js/visual-effects.js"></script>
  
  <script>
    // 比较功能
    let compareList = [];
    
    function addToCompare(productId) {
      if (compareList.includes(productId)) {
        alert('该产品已在比较列表中');
        return;
      }
      
      if (compareList.length >= 3) {
        alert('最多只能比较3个产品');
        return;
      }
      
      compareList.push(productId);
      updateCompareButton();
      
      // 创建提示效果
      const toast = document.createElement('div');
      toast.className = 'toast-notification';
      toast.textContent = '已添加到比较列表';
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.remove();
      }, 3000);
    }
    
    function updateCompareButton() {
      const compareBtn = document.querySelector('.compare-btn');
      if (compareBtn) {
        compareBtn.textContent = `比较 (${compareList.length})`;
        compareBtn.style.display = compareList.length > 0 ? 'block' : 'none';
      }
    }

    // 四季卡片交互效果
    document.addEventListener('DOMContentLoaded', function() {
      const seasonCards = document.querySelectorAll('.season-card');
      
      seasonCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-10px) rotateY(5deg)';
        });
        
        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) rotateY(0)';
        });
      });
    });
  </script>

  <!-- Floating Chat System -->
  <link rel="stylesheet" href="../../css/floating-chat.css">
  <script defer src="../../js/floating-chat.js"></script>
</body>
</html>