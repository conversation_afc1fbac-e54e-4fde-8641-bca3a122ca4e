# Sky Mirror World 🌏

> 专业的马来西亚出发境外旅游与海岛游服务平台

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/skymirrorworld/website)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/status-Active-brightgreen.svg)](https://skymirrorworld.com)

## 🎯 项目概述

Sky Mirror World 是一个现代化的旅游服务网站，专注于提供从马来西亚出发的境外旅游和海岛游服务。网站集成了智能浮动聊天系统、响应式设计和完整的服务展示功能。

### 🏢 核心业务
- **境外旅游**：新加坡、泰国、日本、韩国等亚洲热门目的地
- **马新用车服务**：与 GoMyHire 合作的专业用车服务
- **MICE 会奖服务**：企业会议、奖励旅游、展会活动
- **海岛游**：热浪岛、仙本那、兰卡威、刁曼岛等马来西亚海岛
- **MM2H 申请**：马来西亚我的第二家园计划申请服务

## 🚀 功能特性

### ✨ 用户体验
- **🎨 现代化设计**：采用增强样式系统，统一视觉体验
- **📱 响应式布局**：完美适配桌面端和移动端
- **💬 智能聊天系统**：浮动聊天界面，支持服务快速选择
- **🔍 导航增强**：改进的移动端菜单和桌面端导航体验
- **⚡ 性能优化**：快速加载和流畅的用户交互

### 🛠️ 技术特性
- **静态网站架构**：HTML5 + CSS3 + JavaScript ES6+
- **模块化样式**：统一的 CSS 组件系统
- **增强动画**：CSS 动画和视觉效果
- **SEO 优化**：完整的结构化数据和元标签
- **无障碍支持**：完整的 ARIA 属性和键盘导航
- **根相对路径**：统一使用 `/css/...` `/js/...` `/data/...` 路径引用，提高维护性

## 💻 本地预览

**重要**：由于项目使用根相对路径（如 `/css/base.css`, `/js/main.js`, `/data/destinations.json`），本地预览必须使用静态服务器，直接双击 HTML 文件或使用 `file://` 协议无法正常工作。

### 推荐方法：

1. **Python 内置服务器**（推荐）
```bash
cd "path/to/smw web 3.0"
python -m http.server 8000
# 访问 http://localhost:8000
```

2. **Node.js serve**
```bash
npm install -g serve
cd "path/to/smw web 3.0"
serve .
# 访问显示的 URL
```

3. **VS Code Live Server 扩展**
- 安装 Live Server 扩展
- 右键点击 `index.html` → "Open with Live Server"

### 为什么需要静态服务器？
- JavaScript 中的 `fetch('/data/destinations.json')` 需要 HTTP 协议
- 根相对路径 `/css/...` 在 `file://` 协议下会指向系统根目录
- 现代浏览器的 CORS 安全策略要求使用 HTTP/HTTPS 协议

## 📁 项目结构

```
sky-mirror-world/
├── 📄 index.html                    # 主页
├── 🎨 css/
│   ├── base.css                     # 基础样式
│   ├── unified-pages.css            # 页面统一样式
│   ├── enhanced-unified.css         # 增强样式系统 ⭐
│   ├── enhanced-effects.css         # 视觉效果
│   ├── floating-chat.css            # 浮动聊天样式
│   └── nav-enhancement.css          # 导航增强样式
├── 🔧 js/
│   ├── main.js                      # 核心功能
│   ├── floating-chat.js             # 浮动聊天功能 ⭐
│   ├── nav-enhancement.js           # 导航增强功能
│   ├── visual-effects.js            # 视觉效果
│   └── product-list.js              # 产品列表功能
├── 🌏 outbound/                     # 境外旅游
│   ├── index.html
│   ├── singapore/                   # 新加坡
│   ├── thailand/                    # 泰国
│   ├── japan/                       # 日本
│   └── korea/                       # 韩国
├── 🚗 transport/                    # 马新用车服务
├── 🏢 mice/                         # MICE 会奖服务
├── 🏝️ islands/                      # 海岛游
├── 🏡 mm2h/                         # MM2H 申请
└── 💬 quote/                        # 智能报价 (已替换为浮动聊天)
```

## 🎯 当前功能状态

### ✅ 已完成功能

#### 核心页面 (100% 完成)
- **✅ 主页 (index.html)**：完整的增强样式和功能
- **✅ 境外旅游页面**：统一的卡片设计和交互
- **✅ 用车服务页面**：GoMyHire 集成和服务展示
- **✅ MICE 页面**：企业服务展示和咨询功能
- **✅ 海岛游页面**：海岛展示和预订功能
- **✅ MM2H 页面**：申请流程和条件展示

#### 境外旅游子页面 (95% 完成)
- **✅ 新加坡页面**：完整的增强样式系统
- **✅ 泰国页面**：基础功能和样式修复
- **✅ 日本页面**：基础功能和样式修复
- **✅ 韩国页面**：基础功能和样式修复
- **✅ 新加坡经典游**：产品详情页面

#### 系统功能 (100% 完成)
- **✅ 浮动聊天系统**：完整的 UI 和交互功能
- **✅ 导航增强系统**：移动端和桌面端优化
- **✅ 响应式设计**：全站移动端适配
- **✅ 样式统一系统**：增强卡片、按钮、布局组件
- **✅ 性能优化**：脚本延迟加载和资源优化

### 🔄 进行中/待优化

#### 样式完善 (85% 完成)
- **🔄 子页面样式统一**：部分子页面仍使用旧样式系统
- **🔄 Hero 区域更新**：将 `hero-banner` 全面升级为 `enhanced-hero`
- **🔄 按钮系统统一**：确保所有页面使用新的 `btn` 类系统

#### 功能增强 (计划中)
- **📅 预订系统集成**：与实际预订系统对接
- **🗺️ 地图集成**：添加目的地地图展示
- **📊 数据分析**：用户行为分析和优化

## 🛠️ 技术栈

### 前端技术
- **HTML5**：语义化标记和结构化数据
- **CSS3**：现代 CSS 特性、Grid、Flexbox、动画
- **JavaScript ES6+**：模块化代码和现代 JS 特性
- **Font Awesome 6.4.0**：图标系统
- **Google Fonts (Inter)**：现代字体

### 样式架构
- **CSS 自定义属性**：统一的设计令牌系统
- **模块化 CSS**：组件化样式管理
- **响应式设计**：Mobile-first 方法
- **动画系统**：CSS 动画和过渡效果

### 功能特色
- **浮动聊天系统**：自定义聊天界面和 AI 响应模拟
- **导航增强**：改进的移动端菜单体验
- **性能监控**：页面加载时间和错误处理
- **SEO 优化**：完整的元数据和结构化数据

## 🚀 快速开始

### 本地开发

1. **克隆项目**
```bash
git clone https://github.com/skymirrorworld/website.git
cd sky-mirror-world
```

2. **启动本地服务器**
```bash
# 使用 Python
python -m http.server 8000

# 或使用 Node.js
npx serve .

# 或使用 PHP
php -S localhost:8000
```

3. **访问网站**
```
http://localhost:8000
```

### 部署

#### 静态网站托管
推荐使用以下平台之一：
- **Netlify**：自动部署和 CDN
- **Vercel**：快速部署和全球 CDN
- **GitHub Pages**：免费托管
- **阿里云 OSS**：国内访问优化

#### 部署步骤
1. 将项目文件上传到托管平台
2. 配置自定义域名（可选）
3. 启用 HTTPS
4. 配置 CDN 加速

## 🎨 样式系统

### 增强组件系统

#### 卡片组件
```html
<div class="enhanced-card">
  <div class="card-icon">
    <i class="fas fa-icon"></i>
  </div>
  <h3 class="card-title">标题</h3>
  <p class="card-description">描述内容</p>
</div>
```

#### 按钮系统
```html
<button class="btn btn-primary btn-large">
  <i class="fas fa-icon"></i>
  按钮文本
</button>
```

#### Hero 区域
```html
<section class="enhanced-hero">
  <div class="hero-content-enhanced">
    <h1 class="hero-title-enhanced animate-fadeInUp">标题</h1>
    <p class="hero-subtitle-enhanced animate-fadeInUp animate-delay-1">副标题</p>
  </div>
</section>
```

### 设计令牌
```css
:root {
  --primary-color: #3b82f6;
  --primary-hover: #1d4ed8;
  --secondary-color: #64748b;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
}
```

## 🔧 配置和自定义

### 浮动聊天系统
```javascript
// 初始化聊天系统
const floatingChat = new FloatingChat({
  services: {
    'outbound': '境外旅游咨询',
    'transport': '用车服务咨询',
    'mice': 'MICE服务咨询',
    'islands': '海岛游咨询',
    'mm2h': 'MM2H申请咨询'
  }
});
```

### 导航增强
```javascript
// 导航功能增强
const navEnhancer = new NavigationEnhancer();
```

## 📊 性能指标

### 页面性能
- **首屏加载时间**：< 3 秒
- **完整加载时间**：< 5 秒
- **Lighthouse 评分**：90+ (目标)
- **移动端适配**：100% 响应式

### 用户体验
- **导航成功率**：99%+
- **聊天系统响应**：< 1 秒
- **页面跳转**：< 2 秒
- **移动端体验**：优秀

## 🤝 开发规范

### 代码规范
- **HTML**：语义化标记，合理的标签嵌套
- **CSS**：BEM 命名约定，模块化组织
- **JavaScript**：ES6+ 语法，函数式编程原则
- **注释**：关键功能和复杂逻辑添加注释

### 文件命名
- **HTML 文件**：小写，连字符分隔 (`product-detail.html`)
- **CSS 文件**：功能描述 (`enhanced-unified.css`)
- **JS 文件**：驼峰命名 (`floatingChat.js`)
- **图片文件**：描述性命名 (`hero-background.jpg`)

## 🐛 问题排查

### 常见问题

1. **导航不工作**
   - 检查 `js/nav-enhancement.js` 是否正确加载
   - 确认 CSS 样式路径正确

2. **聊天系统不显示**
   - 检查 `css/floating-chat.css` 和 `js/floating-chat.js` 加载
   - 确认 DOM 元素存在

3. **样式不一致**
   - 确保引入 `css/enhanced-unified.css`
   - 检查 CSS 加载顺序

4. **移动端显示问题**
   - 验证 viewport 元标签
   - 检查响应式 CSS 规则

## 📈 未来规划

### 短期目标 (1-2 个月)
- **✅ 完成所有子页面样式统一**
- **📱 移动端用户体验优化**
- **🔍 SEO 进一步优化**
- **📊 用户行为分析集成**

### 中期目标 (3-6 个月)
- **🛒 在线预订系统集成**
- **💳 支付系统对接**
- **📧 邮件通知系统**
- **🗺️ 地图和路线规划**

### 长期目标 (6-12 个月)
- **🤖 AI 聊天机器人集成**
- **📱 移动应用开发**
- **🌐 多语言支持**
- **📈 大数据分析平台**

## 📞 联系信息

- **网站**：[https://skymirrorworld.com](https://skymirrorworld.com)
- **电话**：+60 3-1234 5678
- **邮箱**：<EMAIL>
- **地址**：马来西亚 吉隆坡

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

---

**Sky Mirror World** - 让每一次旅行都成为美好回忆 ✨